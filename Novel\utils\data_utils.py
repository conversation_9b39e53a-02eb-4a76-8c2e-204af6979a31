"""
数据处理工具模块

基于LLM-SRec的数据处理逻辑，提供推荐系统数据加载和预处理功能：
1. 支持Movies_and_TV和Industrial_and_Scientific数据集
2. 序列数据加载和预处理
3. 文本特征提取和管理
4. 数据集统计和验证
"""

import os
import sys
import pickle
import logging
import numpy as np
import torch
from typing import Dict, List, Tuple, Optional, Any, Union
from collections import defaultdict
from torch.utils.data import Dataset, DataLoader
import random

logger = logging.getLogger(__name__)


class RecommendationDataset(Dataset):
    """
    推荐系统数据集类
    
    支持序列推荐数据的加载和预处理，兼容LLM-SRec数据格式
    """
    
    def __init__(
        self,
        user_sequences: Dict[int, List[int]],
        text_dict: Dict[str, Dict[int, str]],
        max_sequence_length: int = 128,
        mode: str = 'train'
    ):
        """
        初始化数据集
        
        Args:
            user_sequences: 用户序列字典 {user_id: [item_id1, item_id2, ...]}
            text_dict: 物品文本字典 {'title': {item_id: title}, 'description': {item_id: desc}}
            max_sequence_length: 最大序列长度
            mode: 数据集模式 ('train', 'valid', 'test')
        """
        self.user_sequences = user_sequences
        self.text_dict = text_dict
        self.max_sequence_length = max_sequence_length
        self.mode = mode
        
        # 构建用户列表
        self.users = list(user_sequences.keys())
        
        # 获取物品数量
        all_items = set()
        for seq in user_sequences.values():
            all_items.update(seq)
        self.item_num = max(all_items) if all_items else 0
        
        logger.info(f"Dataset initialized: {len(self.users)} users, {self.item_num} items, mode={mode}")
    
    def __len__(self):
        return len(self.users)
    
    def __getitem__(self, idx):
        """
        获取单个样本
        
        Returns:
            dict: 包含用户ID、序列、正样本、负样本等信息
        """
        user_id = self.users[idx]
        sequence = self.user_sequences[user_id]
        
        if self.mode == 'train':
            # 训练模式：随机选择序列中的一个位置作为预测目标
            if len(sequence) < 2:
                # 序列太短，返回空样本
                return self._get_empty_sample(user_id)
            
            # 随机选择切分点
            split_idx = random.randint(1, len(sequence) - 1)
            input_seq = sequence[:split_idx]
            target_item = sequence[split_idx]
            
            # 负采样
            negative_item = self._negative_sampling(user_id, sequence)
            
        else:
            # 验证/测试模式：使用完整序列预测下一个物品
            if len(sequence) < 1:
                return self._get_empty_sample(user_id)
            
            input_seq = sequence[:-1] if len(sequence) > 1 else sequence
            target_item = sequence[-1]
            negative_item = self._negative_sampling(user_id, sequence)
        
        # 序列填充和截断
        padded_seq = self._pad_sequence(input_seq)
        
        return {
            'user_id': user_id,
            'sequence': padded_seq,
            'target_item': target_item,
            'negative_item': negative_item,
            'sequence_length': min(len(input_seq), self.max_sequence_length)
        }
    
    def _pad_sequence(self, sequence: List[int]) -> List[int]:
        """
        序列填充和截断
        
        Args:
            sequence: 原始序列
            
        Returns:
            填充后的序列
        """
        if len(sequence) >= self.max_sequence_length:
            return sequence[-self.max_sequence_length:]
        else:
            return [0] * (self.max_sequence_length - len(sequence)) + sequence
    
    def _negative_sampling(self, user_id: int, user_sequence: List[int]) -> int:
        """
        负采样
        
        Args:
            user_id: 用户ID
            user_sequence: 用户序列
            
        Returns:
            负样本物品ID
        """
        user_items = set(user_sequence)
        while True:
            neg_item = random.randint(1, self.item_num)
            if neg_item not in user_items:
                return neg_item
    
    def _get_empty_sample(self, user_id: int) -> Dict[str, Any]:
        """
        获取空样本（用于处理异常情况）
        
        Args:
            user_id: 用户ID
            
        Returns:
            空样本字典
        """
        return {
            'user_id': user_id,
            'sequence': [0] * self.max_sequence_length,
            'target_item': 0,
            'negative_item': 1,
            'sequence_length': 0
        }


class RecommendationDataLoader:
    """
    推荐系统数据加载器
    
    负责加载和管理LLM-SRec格式的推荐数据集
    """
    
    # 支持的数据集配置
    DATASET_CONFIG = {
        'Movies_and_TV': {
            'description': '电影和电视节目推荐数据集',
            'expected_features': ['title', 'description']
        },
        'Industrial_and_Scientific': {
            'description': '工业和科学产品推荐数据集',
            'expected_features': ['title', 'description']
        }
    }
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化数据加载器
        
        Args:
            config: 数据配置字典
        """
        self.config = config
        self.dataset_name = config['dataset']
        self.data_dir = config['data_dir']
        self.max_sequence_length = config.get('max_sequence_length', 128)
        
        # 验证数据集支持
        if self.dataset_name not in self.DATASET_CONFIG:
            raise ValueError(f"Unsupported dataset: {self.dataset_name}")
        
        logger.info(f"Initializing data loader for dataset: {self.dataset_name}")
        logger.info(f"Dataset description: {self.DATASET_CONFIG[self.dataset_name]['description']}")
        
        # 检查数据文件，如果不存在则自动下载
        self._ensure_data_available()

        # 加载数据
        self._load_datasets()
        
        logger.info("Data loader initialization completed")

    def _ensure_data_available(self):
        """确保数据可用，如果不存在则自动下载"""
        if not self._check_data_files_exist():
            logger.info(f"Dataset {self.dataset_name} not found, starting automatic download...")
            self._auto_download_dataset()
        else:
            logger.info(f"Dataset {self.dataset_name} found, skipping download")

    def _check_data_files_exist(self) -> bool:
        """检查数据文件是否存在（不抛出异常）"""
        dataset_dir = os.path.join(self.data_dir, self.dataset_name)

        required_files = [
            f'{self.dataset_name}_train.txt',
            f'{self.dataset_name}_valid.txt',
            f'{self.dataset_name}_test.txt',
            'text_name_dict.json.gz'
        ]

        for file in required_files:
            file_path = os.path.join(dataset_dir, file)
            if not os.path.exists(file_path):
                return False

        return True

    def _auto_download_dataset(self):
        """自动下载和预处理数据集"""
        try:
            # 首先尝试从LLM-SRec目录复制数据
            if self._try_copy_from_llm_srec():
                logger.info(f"Dataset {self.dataset_name} copied from LLM-SRec successfully")
                return

            # 如果复制失败，尝试自动下载
            from scripts.data_preprocess import AmazonDataPreprocessor

            logger.info(f"Starting automatic download and preprocessing for {self.dataset_name}")

            # 创建预处理器
            preprocessor = AmazonDataPreprocessor(self.dataset_name, self.data_dir)

            # 执行预处理
            preprocessor.preprocess_dataset(force_download=False)

            logger.info(f"Dataset {self.dataset_name} downloaded and processed successfully")

        except ImportError as e:
            logger.error(f"Cannot import data preprocessor or missing dependencies: {e}")
            logger.info("Please install required dependencies: pip install datasets")
            raise
        except Exception as e:
            logger.error(f"Failed to auto-download dataset {self.dataset_name}: {e}")
            raise

    def _try_copy_from_llm_srec(self) -> bool:
        """尝试从LLM-SRec目录复制数据"""
        try:
            import shutil

            # 查找LLM-SRec目录
            possible_paths = [
                f"../LLM-SRec-master/SeqRec/data_{self.dataset_name}",
                f"./LLM-SRec-master/SeqRec/data_{self.dataset_name}",
                f"../LLM-SRec/SeqRec/data_{self.dataset_name}",
                f"./LLM-SRec/SeqRec/data_{self.dataset_name}"
            ]

            source_dir = None
            for path in possible_paths:
                if os.path.exists(path):
                    source_dir = path
                    break

            if source_dir is None:
                logger.info("LLM-SRec data directory not found, will try automatic download")
                return False

            # 创建目标目录
            target_dir = os.path.join(self.data_dir, self.dataset_name)
            os.makedirs(target_dir, exist_ok=True)

            # 复制文件
            required_files = [
                f'{self.dataset_name}_train.txt',
                f'{self.dataset_name}_valid.txt',
                f'{self.dataset_name}_test.txt',
                'text_name_dict.json.gz'
            ]

            for file in required_files:
                source_file = os.path.join(source_dir, file)
                target_file = os.path.join(target_dir, file)

                if os.path.exists(source_file):
                    shutil.copy2(source_file, target_file)
                    logger.info(f"Copied {file}")
                else:
                    logger.warning(f"File {file} not found in LLM-SRec directory")
                    return False

            logger.info(f"Successfully copied {self.dataset_name} from LLM-SRec directory")
            return True

        except Exception as e:
            logger.info(f"Failed to copy from LLM-SRec directory: {e}")
            return False

    def _check_data_files(self):
        """检查必需的数据文件是否存在"""
        dataset_dir = os.path.join(self.data_dir, self.dataset_name)
        
        required_files = [
            f'{self.dataset_name}_train.txt',
            f'{self.dataset_name}_valid.txt', 
            f'{self.dataset_name}_test.txt',
            'text_name_dict.json.gz'
        ]
        
        missing_files = []
        for file in required_files:
            file_path = os.path.join(dataset_dir, file)
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            raise FileNotFoundError(f"Missing required data files: {missing_files}")
        
        logger.info(f"All required data files found for dataset: {self.dataset_name}")
    
    def _load_datasets(self):
        """加载所有数据集"""
        dataset_dir = os.path.join(self.data_dir, self.dataset_name)
        
        logger.info(f"Loading {self.dataset_name} datasets...")
        
        # 加载文本字典
        text_dict_path = os.path.join(dataset_dir, 'text_name_dict.json.gz')
        with open(text_dict_path, 'rb') as f:
            self.text_dict = pickle.load(f)
        
        # 验证文本字典结构
        self._validate_text_dict()
        
        # 加载训练、验证、测试数据
        self.train_sequences = self._load_sequence_data(dataset_dir, 'train')
        self.valid_sequences = self._load_sequence_data(dataset_dir, 'valid')
        self.test_sequences = self._load_sequence_data(dataset_dir, 'test')
        
        logger.info("All datasets loaded successfully")
        
        # 打印数据集统计信息
        self._print_dataset_stats()

    def _validate_text_dict(self):
        """验证文本字典结构"""
        expected_features = self.DATASET_CONFIG[self.dataset_name]['expected_features']

        logger.info(f"Loaded text dictionary with features: {list(self.text_dict.keys())}")
        logger.info(f"Expected features: {expected_features}")

        for feature in expected_features:
            if feature not in self.text_dict:
                logger.warning(f"Missing expected feature: {feature}")
            else:
                logger.info(f"Text dictionary contains {len(self.text_dict[feature])} items")

    def _load_sequence_data(self, dataset_dir: str, split: str) -> Dict[int, List[int]]:
        """
        加载序列数据

        Args:
            dataset_dir: 数据集目录
            split: 数据分割 ('train', 'valid', 'test')

        Returns:
            用户序列字典
        """
        file_path = os.path.join(dataset_dir, f'{self.dataset_name}_{split}.txt')

        user_sequences = defaultdict(list)

        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    user_id, item_id = map(int, line.split())
                    user_sequences[user_id].append(item_id)

        logger.info(f"Loaded {len(user_sequences)} user sequences from {file_path}")
        return dict(user_sequences)

    def _print_dataset_stats(self):
        """打印数据集统计信息"""
        def get_stats(sequences):
            if not sequences:
                return 0, 0, 0, 0

            num_users = len(sequences)
            num_interactions = sum(len(seq) for seq in sequences.values())
            avg_seq_length = num_interactions / num_users if num_users > 0 else 0
            max_seq_length = max(len(seq) for seq in sequences.values()) if sequences else 0

            return num_users, num_interactions, avg_seq_length, max_seq_length

        train_stats = get_stats(self.train_sequences)
        valid_stats = get_stats(self.valid_sequences)
        test_stats = get_stats(self.test_sequences)

        logger.info("Dataset Statistics:")
        logger.info(f"  Train: {train_stats[0]} users, {train_stats[1]} interactions, "
                   f"avg_len={train_stats[2]:.2f}, max_len={train_stats[3]}")
        logger.info(f"  Valid: {valid_stats[0]} users, {valid_stats[1]} interactions, "
                   f"avg_len={valid_stats[2]:.2f}, max_len={valid_stats[3]}")
        logger.info(f"  Test:  {test_stats[0]} users, {test_stats[1]} interactions, "
                   f"avg_len={test_stats[2]:.2f}, max_len={test_stats[3]}")

    def get_train_loader(self, batch_size: int = 32, shuffle: bool = True) -> DataLoader:
        """
        获取训练数据加载器

        Args:
            batch_size: 批次大小
            shuffle: 是否打乱数据

        Returns:
            训练数据加载器
        """
        dataset = RecommendationDataset(
            self.train_sequences,
            self.text_dict,
            self.max_sequence_length,
            mode='train'
        )

        return DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            collate_fn=self._collate_fn
        )

    def get_val_loader(self, batch_size: int = 32, shuffle: bool = False) -> DataLoader:
        """
        获取验证数据加载器

        Args:
            batch_size: 批次大小
            shuffle: 是否打乱数据

        Returns:
            验证数据加载器
        """
        dataset = RecommendationDataset(
            self.valid_sequences,
            self.text_dict,
            self.max_sequence_length,
            mode='valid'
        )

        return DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            collate_fn=self._collate_fn
        )

    def get_test_loader(self, batch_size: int = 32, shuffle: bool = False) -> DataLoader:
        """
        获取测试数据加载器

        Args:
            batch_size: 批次大小
            shuffle: 是否打乱数据

        Returns:
            测试数据加载器
        """
        dataset = RecommendationDataset(
            self.test_sequences,
            self.text_dict,
            self.max_sequence_length,
            mode='test'
        )

        return DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            collate_fn=self._collate_fn
        )

    def _collate_fn(self, batch: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        """
        批次数据整理函数

        Args:
            batch: 批次数据列表

        Returns:
            整理后的批次数据
        """
        user_ids = torch.tensor([item['user_id'] for item in batch], dtype=torch.long)
        sequences = torch.tensor([item['sequence'] for item in batch], dtype=torch.long)
        target_items = torch.tensor([item['target_item'] for item in batch], dtype=torch.long)
        negative_items = torch.tensor([item['negative_item'] for item in batch], dtype=torch.long)
        sequence_lengths = torch.tensor([item['sequence_length'] for item in batch], dtype=torch.long)

        return {
            'user_ids': user_ids,
            'sequences': sequences,
            'target_items': target_items,
            'negative_items': negative_items,
            'sequence_lengths': sequence_lengths
        }

    def get_item_text(self, item_id: int, feature: str = 'title') -> str:
        """
        获取物品文本信息

        Args:
            item_id: 物品ID
            feature: 特征类型 ('title', 'description')

        Returns:
            物品文本信息
        """
        if feature not in self.text_dict:
            return f"Unknown {feature}"

        return self.text_dict[feature].get(item_id, f"Unknown {feature}")

    def get_dataset_info(self) -> Dict[str, Any]:
        """
        获取数据集信息

        Returns:
            数据集信息字典
        """
        # 计算总的用户和物品数量
        all_users = set()
        all_items = set()

        for sequences in [self.train_sequences, self.valid_sequences, self.test_sequences]:
            all_users.update(sequences.keys())
            for seq in sequences.values():
                all_items.update(seq)

        return {
            'dataset_name': self.dataset_name,
            'dataset_description': self.DATASET_CONFIG[self.dataset_name]['description'],
            'total_users': len(all_users),
            'total_items': len(all_items),
            'train_users': len(self.train_sequences),
            'valid_users': len(self.valid_sequences),
            'test_users': len(self.test_sequences),
            'max_sequence_length': self.max_sequence_length,
            'text_features': list(self.text_dict.keys())
        }
