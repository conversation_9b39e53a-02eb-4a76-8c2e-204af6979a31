#!/usr/bin/env python3
"""
完整复现流程演示脚本

演示Novel项目的完整复现流程，包括：
1. 环境检查
2. 数据集准备
3. 训练流程演示
4. 结果验证

这个脚本可以作为README中复现流程的实际验证。
"""

import os
import sys
import subprocess
import logging
import time
import yaml
from typing import List, Dict, Any, Tuple

logger = logging.getLogger(__name__)


class FullReproductionDemo:
    """完整复现流程演示器"""
    
    def __init__(self):
        """初始化演示器"""
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.datasets = ['Movies_and_TV', 'Industrial_and_Scientific']
        self.results = {}
    
    def run_command(self, cmd: List[str], description: str, timeout: int = 60) -> Tuple[bool, str]:
        """运行命令并返回结果"""
        logger.info(f"🔧 {description}")
        logger.info(f"   命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd, 
                cwd=self.script_dir, 
                capture_output=True, 
                text=True, 
                timeout=timeout
            )
            
            if result.returncode == 0:
                logger.info(f"   ✅ 成功")
                return True, result.stdout
            else:
                logger.error(f"   ❌ 失败 (退出码: {result.returncode})")
                if result.stderr:
                    logger.error(f"   错误: {result.stderr.strip()}")
                return False, result.stderr
                
        except subprocess.TimeoutExpired:
            logger.error(f"   ⏰ 超时 ({timeout}秒)")
            return False, "Timeout"
        except Exception as e:
            logger.error(f"   💥 异常: {e}")
            return False, str(e)
    
    def step1_environment_check(self) -> bool:
        """Step 1: 环境检查"""
        logger.info("=" * 60)
        logger.info("Step 1: 环境检查")
        logger.info("=" * 60)
        
        checks = [
            {
                'cmd': ['python', '--version'],
                'desc': '检查Python版本'
            },
            {
                'cmd': ['python', '-c', 'import torch; print(f"PyTorch版本: {torch.__version__}")'],
                'desc': '检查PyTorch安装'
            },
            {
                'cmd': ['python', '-c', 'import numpy; print(f"NumPy版本: {numpy.__version__}")'],
                'desc': '检查NumPy安装'
            },
            {
                'cmd': ['python', '-c', 'import yaml; print("YAML支持正常")'],
                'desc': '检查YAML支持'
            }
        ]
        
        all_passed = True
        for check in checks:
            success, output = self.run_command(check['cmd'], check['desc'])
            if not success:
                all_passed = False
        
        self.results['environment_check'] = all_passed
        return all_passed
    
    def step2_dataset_preparation(self) -> bool:
        """Step 2: 数据集准备和验证"""
        logger.info("\n" + "=" * 60)
        logger.info("Step 2: 数据集准备和验证")
        logger.info("=" * 60)
        
        # 检查数据集状态
        success, output = self.run_command(
            ['python', 'dataset_manager.py', '--list'],
            '查看支持的数据集'
        )
        
        if not success:
            self.results['dataset_preparation'] = False
            return False
        
        # 验证现有数据集
        dataset_results = {}
        for dataset in self.datasets:
            success, output = self.run_command(
                ['python', 'dataset_manager.py', '--dataset', dataset, '--action', 'validate'],
                f'验证{dataset}数据集',
                timeout=120
            )
            dataset_results[dataset] = success
        
        all_datasets_ready = all(dataset_results.values())
        self.results['dataset_preparation'] = all_datasets_ready
        self.results['dataset_status'] = dataset_results
        
        return all_datasets_ready
    
    def step3_training_demo(self) -> bool:
        """Step 3: 训练演示（快速测试）"""
        logger.info("\n" + "=" * 60)
        logger.info("Step 3: 训练演示（快速测试）")
        logger.info("=" * 60)
        
        # 注意：这里只演示训练启动，不进行完整训练
        training_results = {}
        
        for dataset in self.datasets:
            logger.info(f"\n🎯 演示{dataset}数据集训练启动")
            
            # 测试训练脚本的参数解析
            success, output = self.run_command(
                ['python', 'training/collaborative_trainer.py', '--help'],
                f'检查训练脚本帮助信息',
                timeout=30
            )
            
            if success:
                logger.info(f"   ✅ {dataset}训练脚本可用")
                training_results[dataset] = True
            else:
                logger.error(f"   ❌ {dataset}训练脚本不可用")
                training_results[dataset] = False
        
        # 测试训练启动器
        success, output = self.run_command(
            ['python', 'train.py', '--help'],
            '检查训练启动器帮助信息',
            timeout=30
        )
        
        training_results['launcher'] = success
        
        all_training_ready = all(training_results.values())
        self.results['training_demo'] = all_training_ready
        self.results['training_status'] = training_results
        
        return all_training_ready
    
    def step4_cli_functionality(self) -> bool:
        """Step 4: CLI功能测试"""
        logger.info("\n" + "=" * 60)
        logger.info("Step 4: CLI功能测试")
        logger.info("=" * 60)
        
        # 运行CLI测试
        success, output = self.run_command(
            ['python', 'test_cli.py'],
            'CLI功能完整测试',
            timeout=180
        )
        
        self.results['cli_functionality'] = success
        return success
    
    def step5_configuration_test(self) -> bool:
        """Step 5: 配置文件测试"""
        logger.info("\n" + "=" * 60)
        logger.info("Step 5: 配置文件测试")
        logger.info("=" * 60)
        
        try:
            # 测试配置文件加载
            config_path = os.path.join(self.script_dir, 'config', 'collaborative_config.yaml')
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            logger.info("✅ 配置文件加载成功")
            logger.info(f"   当前数据集: {config['data']['dataset']}")
            logger.info(f"   支持的数据集: {list(config['data']['dataset_configs'].keys())}")
            
            # 测试数据集切换
            for dataset in self.datasets:
                success, output = self.run_command(
                    ['python', 'dataset_manager.py', '--dataset', dataset, '--action', 'set-default'],
                    f'设置{dataset}为默认数据集'
                )
                
                if not success:
                    self.results['configuration_test'] = False
                    return False
            
            # 恢复原始设置
            self.run_command(
                ['python', 'dataset_manager.py', '--dataset', 'Movies_and_TV', '--action', 'set-default'],
                '恢复默认数据集设置'
            )
            
            self.results['configuration_test'] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ 配置文件测试失败: {e}")
            self.results['configuration_test'] = False
            return False
    
    def run_full_reproduction(self) -> Dict[str, Any]:
        """运行完整复现流程"""
        logger.info("🎬 Novel项目完整复现流程演示")
        logger.info("=" * 60)
        logger.info("这个演示验证README中描述的所有复现步骤")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        # 执行所有步骤
        steps = [
            ('环境检查', self.step1_environment_check),
            ('数据集准备', self.step2_dataset_preparation),
            ('训练演示', self.step3_training_demo),
            ('CLI功能测试', self.step4_cli_functionality),
            ('配置文件测试', self.step5_configuration_test)
        ]
        
        for step_name, step_func in steps:
            try:
                success = step_func()
                if not success:
                    logger.warning(f"⚠️ {step_name}未完全通过，但继续执行后续步骤")
            except Exception as e:
                logger.error(f"❌ {step_name}执行异常: {e}")
                self.results[step_name.lower().replace(' ', '_')] = False
        
        end_time = time.time()
        self.results['total_time'] = end_time - start_time
        
        # 打印最终结果
        self._print_final_results()
        
        return self.results
    
    def _print_final_results(self):
        """打印最终结果摘要"""
        logger.info("\n" + "=" * 60)
        logger.info("🎯 完整复现流程结果摘要")
        logger.info("=" * 60)
        
        total_steps = 0
        passed_steps = 0
        
        step_results = [
            ('环境检查', self.results.get('environment_check', False)),
            ('数据集准备', self.results.get('dataset_preparation', False)),
            ('训练演示', self.results.get('training_demo', False)),
            ('CLI功能测试', self.results.get('cli_functionality', False)),
            ('配置文件测试', self.results.get('configuration_test', False))
        ]
        
        for step_name, result in step_results:
            total_steps += 1
            if result:
                passed_steps += 1
                logger.info(f"✅ {step_name}: 通过")
            else:
                logger.info(f"❌ {step_name}: 失败")
        
        logger.info(f"\n📊 总体结果: {passed_steps}/{total_steps} 步骤通过")
        logger.info(f"⏱️ 总耗时: {self.results.get('total_time', 0):.1f} 秒")
        
        if passed_steps == total_steps:
            logger.info("\n🎉 恭喜！所有复现步骤都成功完成！")
            logger.info("✨ Novel项目已准备就绪，可以开始使用了！")
            logger.info("\n🚀 快速开始命令:")
            logger.info("   python train.py --dataset Movies_and_TV")
            logger.info("   python train.py --dataset Industrial_and_Scientific")
        else:
            logger.warning(f"\n⚠️ {total_steps - passed_steps} 个步骤未通过")
            logger.info("请检查上述错误信息并修复相关问题")
            logger.info("大部分功能应该仍然可用")
        
        logger.info("\n📚 更多信息请查看:")
        logger.info("   - README.md: 完整使用指南")
        logger.info("   - docs/cli_usage_guide.md: 命令行使用指南")
        logger.info("   - docs/dataset_integration_summary.md: 数据集集成总结")


def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 创建演示器并运行完整复现流程
    demo = FullReproductionDemo()
    results = demo.run_full_reproduction()
    
    # 根据结果设置退出码
    if not all(results.get(key, False) for key in ['environment_check', 'dataset_preparation']):
        sys.exit(1)


if __name__ == "__main__":
    main()
