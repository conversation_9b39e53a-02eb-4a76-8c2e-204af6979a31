"""
推荐系统评估模块

提供推荐系统常用的评估指标，包括：
1. NDCG (Normalized Discounted Cumulative Gain)
2. Recall
3. Precision
4. Hit Rate (HR)
5. Mean Reciprocal Rank (MRR)
"""

import numpy as np
import torch
from typing import List, Dict, Any, Union, Tuple
import logging

logger = logging.getLogger(__name__)


class RecommendationEvaluator:
    """推荐系统评估器"""
    
    def __init__(self, k_values: List[int] = [5, 10, 20]):
        """
        初始化评估器
        
        Args:
            k_values: 评估的top-k值列表
        """
        self.k_values = k_values
        
    def evaluate_batch(
        self, 
        predictions: torch.Tensor, 
        targets: torch.Tensor,
        k_values: List[int] = None
    ) -> Dict[str, float]:
        """
        批量评估推荐结果
        
        Args:
            predictions: 预测分数 [batch_size, num_items]
            targets: 真实标签 [batch_size, num_items] (0/1)
            k_values: 评估的top-k值列表
            
        Returns:
            评估指标字典
        """
        if k_values is None:
            k_values = self.k_values
        
        batch_size = predictions.size(0)
        metrics = {}
        
        # 获取top-k预测
        max_k = max(k_values)
        _, top_k_indices = torch.topk(predictions, max_k, dim=1)
        
        for k in k_values:
            # 计算各项指标
            ndcg_k = self._calculate_ndcg_at_k(top_k_indices[:, :k], targets, k)
            recall_k = self._calculate_recall_at_k(top_k_indices[:, :k], targets)
            precision_k = self._calculate_precision_at_k(top_k_indices[:, :k], targets)
            hr_k = self._calculate_hit_rate_at_k(top_k_indices[:, :k], targets)
            mrr_k = self._calculate_mrr_at_k(top_k_indices[:, :k], targets)
            
            metrics[f'ndcg@{k}'] = ndcg_k
            metrics[f'recall@{k}'] = recall_k
            metrics[f'precision@{k}'] = precision_k
            metrics[f'hr@{k}'] = hr_k
            metrics[f'mrr@{k}'] = mrr_k
        
        return metrics
    
    def _calculate_ndcg_at_k(
        self, 
        top_k_indices: torch.Tensor, 
        targets: torch.Tensor, 
        k: int
    ) -> float:
        """计算NDCG@k"""
        batch_size = top_k_indices.size(0)
        ndcg_scores = []
        
        for i in range(batch_size):
            # 获取真实相关物品
            relevant_items = torch.nonzero(targets[i]).squeeze(-1)
            
            if len(relevant_items) == 0:
                ndcg_scores.append(0.0)
                continue
            
            # 计算DCG
            dcg = 0.0
            for j, item_idx in enumerate(top_k_indices[i]):
                if item_idx in relevant_items:
                    dcg += 1.0 / np.log2(j + 2)  # j+2 because log2(1) = 0
            
            # 计算IDCG
            idcg = 0.0
            for j in range(min(len(relevant_items), k)):
                idcg += 1.0 / np.log2(j + 2)
            
            # 计算NDCG
            if idcg > 0:
                ndcg_scores.append(dcg / idcg)
            else:
                ndcg_scores.append(0.0)
        
        return np.mean(ndcg_scores)
    
    def _calculate_recall_at_k(
        self, 
        top_k_indices: torch.Tensor, 
        targets: torch.Tensor
    ) -> float:
        """计算Recall@k"""
        batch_size = top_k_indices.size(0)
        recall_scores = []
        
        for i in range(batch_size):
            # 获取真实相关物品
            relevant_items = torch.nonzero(targets[i]).squeeze(-1)
            
            if len(relevant_items) == 0:
                recall_scores.append(0.0)
                continue
            
            # 计算命中的相关物品数量
            hits = 0
            for item_idx in top_k_indices[i]:
                if item_idx in relevant_items:
                    hits += 1
            
            # 计算recall
            recall_scores.append(hits / len(relevant_items))
        
        return np.mean(recall_scores)
    
    def _calculate_precision_at_k(
        self, 
        top_k_indices: torch.Tensor, 
        targets: torch.Tensor
    ) -> float:
        """计算Precision@k"""
        batch_size = top_k_indices.size(0)
        k = top_k_indices.size(1)
        precision_scores = []
        
        for i in range(batch_size):
            # 获取真实相关物品
            relevant_items = torch.nonzero(targets[i]).squeeze(-1)
            
            # 计算命中的相关物品数量
            hits = 0
            for item_idx in top_k_indices[i]:
                if item_idx in relevant_items:
                    hits += 1
            
            # 计算precision
            precision_scores.append(hits / k)
        
        return np.mean(precision_scores)
    
    def _calculate_hit_rate_at_k(
        self, 
        top_k_indices: torch.Tensor, 
        targets: torch.Tensor
    ) -> float:
        """计算Hit Rate@k"""
        batch_size = top_k_indices.size(0)
        hit_count = 0
        
        for i in range(batch_size):
            # 获取真实相关物品
            relevant_items = torch.nonzero(targets[i]).squeeze(-1)
            
            if len(relevant_items) == 0:
                continue
            
            # 检查是否有命中
            hit = False
            for item_idx in top_k_indices[i]:
                if item_idx in relevant_items:
                    hit = True
                    break
            
            if hit:
                hit_count += 1
        
        return hit_count / batch_size
    
    def _calculate_mrr_at_k(
        self, 
        top_k_indices: torch.Tensor, 
        targets: torch.Tensor
    ) -> float:
        """计算Mean Reciprocal Rank@k"""
        batch_size = top_k_indices.size(0)
        rr_scores = []
        
        for i in range(batch_size):
            # 获取真实相关物品
            relevant_items = torch.nonzero(targets[i]).squeeze(-1)
            
            if len(relevant_items) == 0:
                rr_scores.append(0.0)
                continue
            
            # 找到第一个命中的位置
            rr = 0.0
            for j, item_idx in enumerate(top_k_indices[i]):
                if item_idx in relevant_items:
                    rr = 1.0 / (j + 1)
                    break
            
            rr_scores.append(rr)
        
        return np.mean(rr_scores)
    
    def evaluate_single(
        self, 
        predictions: List[int], 
        targets: List[int],
        k_values: List[int] = None
    ) -> Dict[str, float]:
        """
        单个样本评估
        
        Args:
            predictions: 预测的物品ID列表（按分数排序）
            targets: 真实的相关物品ID列表
            k_values: 评估的top-k值列表
            
        Returns:
            评估指标字典
        """
        if k_values is None:
            k_values = self.k_values
        
        metrics = {}
        target_set = set(targets)
        
        for k in k_values:
            top_k_preds = predictions[:k]
            
            # NDCG@k
            dcg = 0.0
            for i, item_id in enumerate(top_k_preds):
                if item_id in target_set:
                    dcg += 1.0 / np.log2(i + 2)
            
            idcg = 0.0
            for i in range(min(len(targets), k)):
                idcg += 1.0 / np.log2(i + 2)
            
            ndcg = dcg / idcg if idcg > 0 else 0.0
            
            # Recall@k
            hits = len(set(top_k_preds) & target_set)
            recall = hits / len(targets) if len(targets) > 0 else 0.0
            
            # Precision@k
            precision = hits / k if k > 0 else 0.0
            
            # Hit Rate@k
            hr = 1.0 if hits > 0 else 0.0
            
            # MRR@k
            mrr = 0.0
            for i, item_id in enumerate(top_k_preds):
                if item_id in target_set:
                    mrr = 1.0 / (i + 1)
                    break
            
            metrics[f'ndcg@{k}'] = ndcg
            metrics[f'recall@{k}'] = recall
            metrics[f'precision@{k}'] = precision
            metrics[f'hr@{k}'] = hr
            metrics[f'mrr@{k}'] = mrr
        
        return metrics
    
    def aggregate_metrics(self, metric_list: List[Dict[str, float]]) -> Dict[str, float]:
        """
        聚合多个评估结果
        
        Args:
            metric_list: 评估指标字典列表
            
        Returns:
            聚合后的评估指标
        """
        if not metric_list:
            return {}
        
        aggregated = {}
        for key in metric_list[0].keys():
            values = [metrics[key] for metrics in metric_list if key in metrics]
            aggregated[key] = np.mean(values)
        
        return aggregated
