#!/usr/bin/env python3
"""
Novel: Simple data setup script
Download and prepare datasets - Keep it simple like LLM-SRec
"""

import os
import shutil
import argparse

def setup_dataset(dataset_name):
    """Setup dataset by copying from LLM-SRec if available"""
    print(f"Setting up {dataset_name} dataset...")
    
    # Create data directory
    data_dir = './data'
    os.makedirs(data_dir, exist_ok=True)
    
    target_dir = os.path.join(data_dir, dataset_name)
    
    # Try to copy from LLM-SRec directory
    possible_sources = [
        f'../LLM-SRec-master/SeqRec/data_{dataset_name}',
        f'./LLM-SRec-master/SeqRec/data_{dataset_name}',
        f'../LLM-SRec/SeqRec/data_{dataset_name}',
        f'./LLM-SRec/SeqRec/data_{dataset_name}'
    ]
    
    source_dir = None
    for source in possible_sources:
        if os.path.exists(source):
            source_dir = source
            break
    
    if source_dir:
        print(f"Found LLM-SRec data at: {source_dir}")
        if os.path.exists(target_dir):
            shutil.rmtree(target_dir)
        shutil.copytree(source_dir, target_dir)
        print(f"{dataset_name} dataset copied successfully!")
        return True
    else:
        print(f"LLM-SRec data not found for {dataset_name}")
        print("Please ensure LLM-SRec project is in the parent directory")
        print("or download the data manually to ./data/{dataset_name}/")
        return False

def main():
    parser = argparse.ArgumentParser(description='Setup datasets for Novel')
    parser.add_argument('--dataset', type=str, 
                       choices=['Movies_and_TV', 'Industrial_and_Scientific', 'all'],
                       default='all',
                       help='Dataset to setup')
    
    args = parser.parse_args()
    
    datasets = ['Movies_and_TV', 'Industrial_and_Scientific'] if args.dataset == 'all' else [args.dataset]
    
    print("Novel Data Setup")
    print("=" * 40)

    success_count = 0
    for dataset in datasets:
        if setup_dataset(dataset):
            success_count += 1
        print()

    print(f"Setup completed: {success_count}/{len(datasets)} datasets ready")

    if success_count > 0:
        print("\nReady to train! Try:")
        for dataset in datasets[:success_count]:
            print(f"   python main.py --dataset {dataset} --train")

if __name__ == "__main__":
    main()
