"""
LLM-SRec数据集集成演示脚本

展示Novel项目中集成的LLM-SRec数据集功能：
1. 数据集加载和统计
2. 批次数据处理
3. 多数据集切换
4. 配置文件管理
"""

import os
import sys
import yaml
import torch
import logging
from typing import Dict, Any

# 添加项目路径
sys.path.append('.')

from utils.data_utils import RecommendationDataLoader

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DatasetIntegrationDemo:
    """数据集集成演示类"""
    
    def __init__(self):
        """初始化演示"""
        self.datasets = ['Movies_and_TV', 'Industrial_and_Scientific']
        # 获取脚本所在目录，确保路径正确
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.data_dir = os.path.join(script_dir, 'data')
        
        logger.info("🎬 LLM-SRec数据集集成演示开始")
        logger.info("=" * 60)
    
    def demo_dataset_loading(self):
        """演示数据集加载功能"""
        logger.info("📂 演示数据集加载功能")
        
        for dataset_name in self.datasets:
            logger.info(f"\n🔍 加载数据集: {dataset_name}")
            
            config = {
                'dataset': dataset_name,
                'data_dir': self.data_dir,
                'max_sequence_length': 128
            }
            
            try:
                # 加载数据集
                loader = RecommendationDataLoader(config)
                
                # 获取数据集信息
                info = loader.get_dataset_info()
                
                logger.info(f"✅ {dataset_name} 加载成功!")
                logger.info(f"   描述: {info['dataset_description']}")
                logger.info(f"   用户数: {info['total_users']:,}")
                logger.info(f"   物品数: {info['total_items']:,}")
                logger.info(f"   训练用户: {info['train_users']:,}")
                logger.info(f"   验证用户: {info['valid_users']:,}")
                logger.info(f"   测试用户: {info['test_users']:,}")
                
            except Exception as e:
                logger.error(f"❌ {dataset_name} 加载失败: {e}")
    
    def demo_batch_processing(self):
        """演示批次数据处理"""
        logger.info("\n🔄 演示批次数据处理功能")
        
        # 使用Movies_and_TV数据集演示
        config = {
            'dataset': 'Movies_and_TV',
            'data_dir': self.data_dir,
            'max_sequence_length': 128
        }
        
        loader = RecommendationDataLoader(config)
        
        # 创建数据加载器
        train_loader = loader.get_train_loader(batch_size=8, shuffle=True)
        val_loader = loader.get_val_loader(batch_size=8, shuffle=False)
        test_loader = loader.get_test_loader(batch_size=8, shuffle=False)
        
        logger.info(f"📊 数据加载器创建成功:")
        logger.info(f"   训练批次数: {len(train_loader):,}")
        logger.info(f"   验证批次数: {len(val_loader):,}")
        logger.info(f"   测试批次数: {len(test_loader):,}")
        
        # 演示一个训练批次
        logger.info(f"\n🎯 训练批次示例:")
        for batch in train_loader:
            for key, value in batch.items():
                if hasattr(value, 'shape'):
                    logger.info(f"   {key}: {value.shape} ({value.dtype})")
                else:
                    logger.info(f"   {key}: {type(value)}")
            
            # 显示数据范围
            logger.info(f"   数据范围:")
            logger.info(f"     用户ID: {batch['user_ids'].min().item()} - {batch['user_ids'].max().item()}")
            logger.info(f"     序列: {batch['sequences'].min().item()} - {batch['sequences'].max().item()}")
            logger.info(f"     目标物品: {batch['target_items'].min().item()} - {batch['target_items'].max().item()}")
            logger.info(f"     序列长度: {batch['sequence_lengths'].min().item()} - {batch['sequence_lengths'].max().item()}")
            break
    
    def demo_item_text_retrieval(self):
        """演示物品文本获取功能"""
        logger.info("\n📝 演示物品文本获取功能")
        
        for dataset_name in self.datasets:
            config = {
                'dataset': dataset_name,
                'data_dir': self.data_dir,
                'max_sequence_length': 128
            }
            
            loader = RecommendationDataLoader(config)
            
            logger.info(f"\n📚 {dataset_name} 物品文本示例:")
            
            # 获取前3个物品的文本信息
            for item_id in range(1, 4):
                title = loader.get_item_text(item_id, 'title')
                description = loader.get_item_text(item_id, 'description')
                
                logger.info(f"   物品 {item_id}:")
                logger.info(f"     标题: {title[:50]}...")
                logger.info(f"     描述: {description[:50]}...")
    
    def demo_config_management(self):
        """演示配置文件管理"""
        logger.info("\n⚙️ 演示配置文件管理功能")
        
        try:
            # 加载配置文件
            script_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(script_dir, 'config', 'collaborative_config.yaml')
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            logger.info("✅ 配置文件加载成功!")
            
            # 显示数据集配置
            data_config = config['data']
            logger.info(f"📋 数据集配置:")
            logger.info(f"   当前数据集: {data_config['dataset']}")
            logger.info(f"   支持的数据集: {list(data_config['dataset_configs'].keys())}")
            logger.info(f"   多数据集实验: {data_config['multi_dataset_experiment']}")
            
            # 显示数据集特定配置
            logger.info(f"\n🎯 数据集特定配置:")
            for dataset_name, dataset_config in data_config['dataset_configs'].items():
                logger.info(f"   {dataset_name}:")
                logger.info(f"     描述: {dataset_config['description']}")
                logger.info(f"     用户数: {dataset_config['total_users']:,}")
                logger.info(f"     物品数: {dataset_config['total_items']:,}")
                logger.info(f"     领域: {dataset_config['domain']}")
            
            # 显示小模型配置
            small_model_config = config['small_model']
            logger.info(f"\n🤖 小模型数据集特定配置:")
            for dataset_name, model_config in small_model_config['dataset_specific'].items():
                logger.info(f"   {dataset_name}: 物品数={model_config['item_num']:,}, "
                           f"隐藏单元={model_config['hidden_units']}")
            
            # 显示实验配置
            experiment_config = config['experiment']
            logger.info(f"\n🧪 实验配置:")
            logger.info(f"   多数据集实验: {experiment_config['multi_dataset_experiment']}")
            logger.info(f"   跨数据集评估: {experiment_config['cross_dataset_evaluation']}")
            logger.info(f"   数据集对比: {experiment_config['dataset_comparison']}")
            
        except Exception as e:
            logger.error(f"❌ 配置文件管理演示失败: {e}")
    
    def demo_dataset_switching(self):
        """演示数据集切换功能"""
        logger.info("\n🔄 演示数据集切换功能")
        
        base_config = {
            'data_dir': self.data_dir,
            'max_sequence_length': 128
        }
        
        for dataset_name in self.datasets:
            logger.info(f"\n🔀 切换到数据集: {dataset_name}")
            
            config = base_config.copy()
            config['dataset'] = dataset_name
            
            try:
                loader = RecommendationDataLoader(config)
                info = loader.get_dataset_info()
                
                logger.info(f"✅ 切换成功!")
                logger.info(f"   当前数据集: {info['dataset_name']}")
                logger.info(f"   用户数: {info['total_users']:,}")
                logger.info(f"   物品数: {info['total_items']:,}")
                
                # 创建一个小的数据加载器测试
                train_loader = loader.get_train_loader(batch_size=4, shuffle=True)
                logger.info(f"   训练批次数: {len(train_loader):,}")
                
            except Exception as e:
                logger.error(f"❌ 切换到 {dataset_name} 失败: {e}")
    
    def run_demo(self):
        """运行完整演示"""
        try:
            # 1. 数据集加载演示
            self.demo_dataset_loading()
            
            # 2. 批次处理演示
            self.demo_batch_processing()
            
            # 3. 物品文本获取演示
            self.demo_item_text_retrieval()
            
            # 4. 配置管理演示
            self.demo_config_management()
            
            # 5. 数据集切换演示
            self.demo_dataset_switching()
            
            logger.info("\n" + "=" * 60)
            logger.info("🎉 LLM-SRec数据集集成演示完成!")
            logger.info("✅ 所有功能测试通过!")
            logger.info("📚 详细文档请查看: docs/dataset_integration_summary.md")
            logger.info("🚀 现在可以开始使用集成的数据集进行模型训练和实验!")
            
        except Exception as e:
            logger.error(f"❌ 演示过程中出现错误: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    demo = DatasetIntegrationDemo()
    demo.run_demo()


if __name__ == "__main__":
    main()
