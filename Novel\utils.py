"""
Novel: Simplified utilities
Core utility functions for data loading and evaluation
"""

import os
import pickle
import numpy as np
import torch
from collections import defaultdict
from typing import Dict, List, Tuple, Any

def load_dataset(dataset_name: str, data_dir: str = './data'):
    """
    Load dataset in LLM-SRec format
    
    Args:
        dataset_name: Name of dataset (Movies_and_TV, Industrial_and_Scientific)
        data_dir: Data directory
        
    Returns:
        train_data, valid_data, test_data, text_dict
    """
    dataset_dir = os.path.join(data_dir, dataset_name)
    
    # Check if dataset exists
    if not os.path.exists(dataset_dir):
        print(f"❌ Dataset {dataset_name} not found in {dataset_dir}")
        print("💡 Available datasets:")
        if os.path.exists(data_dir):
            for d in os.listdir(data_dir):
                if os.path.isdir(os.path.join(data_dir, d)):
                    print(f"   - {d}")
        print("\n🔧 To download datasets, run:")
        print(f"   python dataset_manager.py --dataset {dataset_name} --action download")
        raise FileNotFoundError(f"Dataset {dataset_name} not found")
    
    print(f"📂 Loading {dataset_name} dataset...")
    
    # Load interaction data
    train_data = load_interactions(dataset_dir, dataset_name, 'train')
    valid_data = load_interactions(dataset_dir, dataset_name, 'valid')
    test_data = load_interactions(dataset_dir, dataset_name, 'test')
    
    # Load text dictionary
    text_dict_path = os.path.join(dataset_dir, 'text_name_dict.json.gz')
    with open(text_dict_path, 'rb') as f:
        text_dict = pickle.load(f)
    
    print(f"✅ Dataset loaded: {len(train_data)} train users, {len(valid_data)} valid users, {len(test_data)} test users")
    
    return train_data, valid_data, test_data, text_dict

def load_interactions(dataset_dir: str, dataset_name: str, split: str) -> Dict[int, List[int]]:
    """Load user-item interactions"""
    file_path = os.path.join(dataset_dir, f'{dataset_name}_{split}.txt')
    
    user_sequences = defaultdict(list)
    with open(file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                user_id, item_id = map(int, line.split())
                user_sequences[user_id].append(item_id)
    
    return dict(user_sequences)

def get_dataset_stats(train_data: Dict, valid_data: Dict, test_data: Dict) -> Dict[str, Any]:
    """Get dataset statistics"""
    all_users = set(train_data.keys()) | set(valid_data.keys()) | set(test_data.keys())
    all_items = set()
    
    for sequences in [train_data, valid_data, test_data]:
        for seq in sequences.values():
            all_items.update(seq)
    
    train_interactions = sum(len(seq) for seq in train_data.values())
    valid_interactions = sum(len(seq) for seq in valid_data.values())
    test_interactions = sum(len(seq) for seq in test_data.values())
    
    return {
        'num_users': len(all_users),
        'num_items': len(all_items),
        'train_interactions': train_interactions,
        'valid_interactions': valid_interactions,
        'test_interactions': test_interactions,
        'total_interactions': train_interactions + valid_interactions + test_interactions
    }

def pad_sequence(sequence: List[int], max_len: int, pad_value: int = 0) -> List[int]:
    """Pad or truncate sequence to max_len"""
    if len(sequence) >= max_len:
        return sequence[-max_len:]
    else:
        return [pad_value] * (max_len - len(sequence)) + sequence

def create_data_loader(user_sequences: Dict[int, List[int]], 
                      batch_size: int, 
                      max_len: int,
                      shuffle: bool = True) -> torch.utils.data.DataLoader:
    """Create PyTorch DataLoader"""
    
    class SeqDataset(torch.utils.data.Dataset):
        def __init__(self, sequences, max_len):
            self.users = list(sequences.keys())
            self.sequences = sequences
            self.max_len = max_len
        
        def __len__(self):
            return len(self.users)
        
        def __getitem__(self, idx):
            user_id = self.users[idx]
            sequence = self.sequences[user_id]
            
            # Pad sequence
            padded_seq = pad_sequence(sequence, self.max_len)
            
            return {
                'user_id': user_id,
                'sequence': torch.tensor(padded_seq, dtype=torch.long),
                'length': min(len(sequence), self.max_len)
            }
    
    dataset = SeqDataset(user_sequences, max_len)
    return torch.utils.data.DataLoader(dataset, batch_size=batch_size, shuffle=shuffle)

def calculate_metrics(predictions: torch.Tensor, targets: torch.Tensor, k: int = 10) -> Dict[str, float]:
    """Calculate recommendation metrics"""
    batch_size = predictions.size(0)
    
    # Get top-k predictions
    _, top_k_indices = torch.topk(predictions, k, dim=1)
    
    # Calculate metrics
    hits = 0
    ndcg_sum = 0.0
    
    for i in range(batch_size):
        target_items = torch.nonzero(targets[i]).squeeze(-1)
        if len(target_items) == 0:
            continue
        
        # Check hits
        pred_items = top_k_indices[i]
        hit_mask = torch.isin(pred_items, target_items)
        
        if hit_mask.any():
            hits += 1
            
            # Calculate NDCG
            hit_positions = torch.nonzero(hit_mask).squeeze(-1)
            if len(hit_positions) > 0:
                dcg = sum(1.0 / np.log2(pos.item() + 2) for pos in hit_positions)
                idcg = sum(1.0 / np.log2(j + 2) for j in range(min(len(target_items), k)))
                ndcg_sum += dcg / idcg if idcg > 0 else 0.0
    
    hr = hits / batch_size
    ndcg = ndcg_sum / batch_size
    
    return {
        f'hr@{k}': hr,
        f'ndcg@{k}': ndcg
    }

def set_seed(seed: int):
    """Set random seed for reproducibility"""
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)

def ensure_dir(path: str):
    """Ensure directory exists"""
    os.makedirs(path, exist_ok=True)

def print_metrics(metrics: Dict[str, float], prefix: str = ""):
    """Print metrics in a nice format"""
    if prefix:
        print(f"{prefix}:")
    for key, value in metrics.items():
        print(f"  {key}: {value:.4f}")
