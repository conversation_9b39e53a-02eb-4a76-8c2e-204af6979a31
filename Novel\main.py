#!/usr/bin/env python3
"""
Novel: LLM-SRec Enhanced Collaborative Recommendation System
Main entry point - Simple and clean like LLM-SRec

Usage:
    python main.py --dataset Movies_and_TV --train
    python main.py --dataset Industrial_and_Scientific --train
"""

import os
import sys
import argparse
import torch

from utils import *
from train_model import *

def main():
    parser = argparse.ArgumentParser(description='Novel: LLM-SRec Enhanced Collaborative Recommendation')
    
    # Core arguments (similar to LLM-SRec)
    parser.add_argument('--dataset', type=str, default='Movies_and_TV', 
                       choices=['Movies_and_TV', 'Industrial_and_Scientific'],
                       help='Dataset to use for training/evaluation')
    parser.add_argument('--train', action='store_true', help='Run training')
    parser.add_argument('--eval', action='store_true', help='Run evaluation')
    
    # Model arguments
    parser.add_argument('--device', type=str, default='0', help='GPU device')
    parser.add_argument('--batch_size', type=int, default=32, help='Batch size')
    parser.add_argument('--num_epochs', type=int, default=50, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=0.001, help='Learning rate')
    parser.add_argument('--maxlen', type=int, default=128, help='Max sequence length')
    
    # Optional arguments
    parser.add_argument('--save_dir', type=str, default='results', help='Save directory')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    
    args = parser.parse_args()
    
    # Setup device
    if args.device == 'cpu':
        args.device = torch.device('cpu')
    else:
        args.device = torch.device(f'cuda:{args.device}')
    
    print(f"Novel: Training on {args.dataset} dataset")
    print(f"Device: {args.device}, Batch size: {args.batch_size}, Epochs: {args.num_epochs}")

    if args.train:
        print("Starting training...")
        train_model(args)
    elif args.eval:
        print("Starting evaluation...")
        eval_model(args)
    else:
        print("Please specify --train or --eval")
        parser.print_help()

if __name__ == "__main__":
    main()
