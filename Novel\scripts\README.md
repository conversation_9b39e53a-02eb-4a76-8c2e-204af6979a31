# 数据预处理脚本使用说明

本目录包含用于处理Amazon 2023数据集的预处理脚本，支持Movies_and_TV和Industrial_and_Scientific两个数据集。

## 脚本说明

### 1. data_preprocess.py
单个数据集预处理脚本，支持从Amazon Reviews 2023数据集自动下载和处理数据。

**功能特性：**
- 自动下载Amazon Reviews 2023数据集
- 数据清洗和过滤（5-core过滤）
- 构建用户-物品交互序列
- 生成物品文本描述映射
- 数据集分割（训练/验证/测试）

**使用方法：**
```bash
# 处理Movies_and_TV数据集
python scripts/data_preprocess.py --dataset Movies_and_TV

# 处理Industrial_and_Scientific数据集
python scripts/data_preprocess.py --dataset Industrial_and_Scientific

# 强制重新下载和处理
python scripts/data_preprocess.py --dataset Movies_and_TV --force_download

# 指定输出目录
python scripts/data_preprocess.py --dataset Movies_and_TV --output_dir ./custom_data
```

### 2. preprocess_datasets.py
批量数据预处理脚本，可以一次性处理多个数据集。

**功能特性：**
- 批量处理多个数据集
- 数据验证和统计
- 处理结果总结
- 错误处理和报告

**使用方法：**
```bash
# 处理所有支持的数据集
python scripts/preprocess_datasets.py

# 处理指定的数据集
python scripts/preprocess_datasets.py --datasets Movies_and_TV Industrial_and_Scientific

# 强制重新下载
python scripts/preprocess_datasets.py --force_download

# 设置日志级别
python scripts/preprocess_datasets.py --log_level DEBUG
```

## 数据集信息

### Movies_and_TV
- **描述**: 电影和电视节目推荐数据集
- **用户数**: ~11,947
- **物品数**: ~17,490
- **交互数**: ~144,071
- **平均序列长度**: ~10.8

### Industrial_and_Scientific
- **描述**: 工业和科学产品推荐数据集
- **用户数**: ~23,627
- **物品数**: ~25,764
- **交互数**: ~266,164
- **平均序列长度**: ~9.3

## 输出文件结构

处理完成后，每个数据集会在`data/`目录下生成以下文件：

```
data/
├── Movies_and_TV/
│   ├── Movies_and_TV_train.txt          # 训练数据
│   ├── Movies_and_TV_valid.txt          # 验证数据
│   ├── Movies_and_TV_test.txt           # 测试数据
│   ├── text_name_dict.json.gz           # 物品文本信息
│   └── Results.txt                      # 处理结果统计
└── Industrial_and_Scientific/
    ├── Industrial_and_Scientific_train.txt
    ├── Industrial_and_Scientific_valid.txt
    ├── Industrial_and_Scientific_test.txt
    ├── text_name_dict.json.gz
    └── Results.txt
```

## 数据格式说明

### 交互数据格式
每行格式为：`user_id item_id`
```
1 1
1 2
1 3
2 4
2 5
```

### 文本字典格式
包含物品的标题、描述和时间信息：
```python
{
    'title': {
        1: 'Movie Title 1',
        2: 'Movie Title 2',
        ...
    },
    'description': {
        1: 'Movie description 1',
        2: 'Movie description 2',
        ...
    },
    'time': {}  # 保持与LLM-SRec格式兼容
}
```

## 依赖要求

确保安装以下Python包：
```bash
pip install datasets
pip install tqdm
pip install numpy
pip install torch
```

## 注意事项

1. **网络连接**: 首次运行需要从HuggingFace下载数据集，确保网络连接稳定
2. **存储空间**: 原始数据集较大，确保有足够的磁盘空间
3. **处理时间**: 数据预处理可能需要较长时间，请耐心等待
4. **内存使用**: 处理大型数据集时可能需要较多内存

## 故障排除

### 常见问题

1. **下载失败**
   - 检查网络连接
   - 尝试使用VPN
   - 重新运行脚本

2. **内存不足**
   - 关闭其他程序释放内存
   - 考虑使用更小的数据集进行测试

3. **文件权限错误**
   - 确保对输出目录有写权限
   - 尝试使用管理员权限运行

### 获取帮助

如果遇到问题，可以：
1. 查看详细的错误日志
2. 使用`--log_level DEBUG`获取更多信息
3. 检查数据文件是否完整生成

## 示例用法

### 快速开始
```bash
# 1. 处理Movies_and_TV数据集
python scripts/data_preprocess.py --dataset Movies_and_TV

# 2. 验证数据加载
python -c "
from utils.data_utils import RecommendationDataLoader
config = {'dataset': 'Movies_and_TV', 'data_dir': './data', 'max_sequence_length': 128}
loader = RecommendationDataLoader(config)
print('Dataset loaded successfully!')
print(loader.get_dataset_info())
"
```

### 批量处理
```bash
# 处理所有数据集并验证
python scripts/preprocess_datasets.py --log_level INFO
```
