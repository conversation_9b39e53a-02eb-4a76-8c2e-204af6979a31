#!/usr/bin/env python3
"""
数据集管理命令行工具

类似LLM-SRec的命令行接口，支持：
1. 通过--dataset参数切换数据集
2. 自动检测和下载缺失的数据集
3. 数据集验证和统计
4. 数据集管理操作

使用示例：
    python dataset_manager.py --dataset Movies_and_TV --action download
    python dataset_manager.py --dataset Industrial_and_Scientific --action validate
    python dataset_manager.py --list
"""

import os
import sys
import argparse
import logging
import yaml
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.data_utils import RecommendationDataLoader
from scripts.data_preprocess import AmazonDataPreprocessor

logger = logging.getLogger(__name__)


class DatasetManager:
    """数据集管理器"""
    
    SUPPORTED_DATASETS = ['Movies_and_TV', 'Industrial_and_Scientific']
    
    def __init__(self, data_dir: str = "./data"):
        """
        初始化数据集管理器
        
        Args:
            data_dir: 数据目录
        """
        self.data_dir = data_dir
        self.config_path = os.path.join(os.path.dirname(__file__), 'config', 'collaborative_config.yaml')
        
    def list_datasets(self):
        """列出所有支持的数据集及其状态"""
        logger.info("📋 支持的数据集列表:")
        logger.info("=" * 60)
        
        for dataset_name in self.SUPPORTED_DATASETS:
            status = self._check_dataset_status(dataset_name)
            status_icon = "✅" if status['available'] else "❌"
            
            logger.info(f"{status_icon} {dataset_name}")
            logger.info(f"   状态: {'已下载' if status['available'] else '未下载'}")
            
            if status['available']:
                logger.info(f"   用户数: {status.get('users', 'N/A'):,}")
                logger.info(f"   物品数: {status.get('items', 'N/A'):,}")
                logger.info(f"   训练交互: {status.get('train_interactions', 'N/A'):,}")
            else:
                logger.info(f"   描述: {self._get_dataset_description(dataset_name)}")
            
            logger.info("")
    
    def download_dataset(self, dataset_name: str, force: bool = False):
        """
        下载数据集
        
        Args:
            dataset_name: 数据集名称
            force: 是否强制重新下载
        """
        if dataset_name not in self.SUPPORTED_DATASETS:
            raise ValueError(f"Unsupported dataset: {dataset_name}")
        
        logger.info(f"📥 下载数据集: {dataset_name}")
        
        # 检查是否已存在
        if not force and self._check_dataset_status(dataset_name)['available']:
            logger.info(f"✅ 数据集 {dataset_name} 已存在，跳过下载")
            logger.info("💡 使用 --force 参数强制重新下载")
            return
        
        try:
            # 创建预处理器并下载
            preprocessor = AmazonDataPreprocessor(dataset_name, self.data_dir)
            preprocessor.preprocess_dataset(force_download=force)
            
            logger.info(f"✅ 数据集 {dataset_name} 下载完成!")
            
            # 显示统计信息
            self.validate_dataset(dataset_name)
            
        except Exception as e:
            logger.error(f"❌ 下载数据集 {dataset_name} 失败: {e}")
            raise
    
    def validate_dataset(self, dataset_name: str):
        """
        验证数据集
        
        Args:
            dataset_name: 数据集名称
        """
        if dataset_name not in self.SUPPORTED_DATASETS:
            raise ValueError(f"Unsupported dataset: {dataset_name}")
        
        logger.info(f"🔍 验证数据集: {dataset_name}")
        
        try:
            config = {
                'dataset': dataset_name,
                'data_dir': self.data_dir,
                'max_sequence_length': 128
            }
            
            # 加载数据集（不自动下载）
            loader = RecommendationDataLoader.__new__(RecommendationDataLoader)
            loader.config = config
            loader.dataset_name = dataset_name
            loader.data_dir = self.data_dir
            loader.max_sequence_length = 128
            
            # 检查文件是否存在
            if not loader._check_data_files_exist():
                logger.error(f"❌ 数据集 {dataset_name} 文件缺失")
                logger.info(f"💡 使用 --action download 下载数据集")
                return
            
            # 加载数据
            loader._load_datasets()
            
            # 获取统计信息
            info = loader.get_dataset_info()
            
            logger.info(f"✅ 数据集 {dataset_name} 验证通过!")
            logger.info(f"   描述: {info['dataset_description']}")
            logger.info(f"   用户数: {info['total_users']:,}")
            logger.info(f"   物品数: {info['total_items']:,}")
            logger.info(f"   训练用户: {info['train_users']:,}")
            logger.info(f"   验证用户: {info['valid_users']:,}")
            logger.info(f"   测试用户: {info['test_users']:,}")
            
        except Exception as e:
            logger.error(f"❌ 验证数据集 {dataset_name} 失败: {e}")
            raise
    
    def set_default_dataset(self, dataset_name: str):
        """
        设置默认数据集
        
        Args:
            dataset_name: 数据集名称
        """
        if dataset_name not in self.SUPPORTED_DATASETS:
            raise ValueError(f"Unsupported dataset: {dataset_name}")
        
        logger.info(f"⚙️ 设置默认数据集: {dataset_name}")
        
        try:
            # 读取配置文件
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 更新数据集设置
            config['data']['dataset'] = dataset_name
            
            # 保存配置文件
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.safe_dump(config, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"✅ 默认数据集已设置为: {dataset_name}")
            
        except Exception as e:
            logger.error(f"❌ 设置默认数据集失败: {e}")
            raise
    
    def clean_dataset(self, dataset_name: str):
        """
        清理数据集

        Args:
            dataset_name: 数据集名称
        """
        if dataset_name not in self.SUPPORTED_DATASETS:
            raise ValueError(f"Unsupported dataset: {dataset_name}")

        dataset_dir = os.path.join(self.data_dir, dataset_name)

        if not os.path.exists(dataset_dir):
            logger.info(f"📁 数据集 {dataset_name} 目录不存在，无需清理")
            return

        logger.info(f"🗑️ 清理数据集: {dataset_name}")

        try:
            import shutil
            shutil.rmtree(dataset_dir)
            logger.info(f"✅ 数据集 {dataset_name} 已清理")

        except Exception as e:
            logger.error(f"❌ 清理数据集 {dataset_name} 失败: {e}")
            raise

    def clean_all_datasets(self):
        """清理所有数据集"""
        logger.info("🗑️ 清理所有数据集")

        for dataset_name in self.SUPPORTED_DATASETS:
            try:
                self.clean_dataset(dataset_name)
            except Exception as e:
                logger.error(f"清理 {dataset_name} 失败: {e}")

        logger.info("✅ 所有数据集清理完成")

    def download_all_datasets(self, force: bool = False):
        """下载所有数据集"""
        logger.info("📥 下载所有数据集")

        results = {}

        for dataset_name in self.SUPPORTED_DATASETS:
            try:
                self.download_dataset(dataset_name, force)
                results[dataset_name] = 'success'
            except Exception as e:
                logger.error(f"下载 {dataset_name} 失败: {e}")
                results[dataset_name] = f'failed: {e}'

        # 打印结果摘要
        logger.info("\n📋 下载结果摘要:")
        for dataset_name, result in results.items():
            status_icon = "✅" if result == 'success' else "❌"
            logger.info(f"{status_icon} {dataset_name}: {result}")

    def validate_all_datasets(self):
        """验证所有数据集"""
        logger.info("🔍 验证所有数据集")

        results = {}

        for dataset_name in self.SUPPORTED_DATASETS:
            try:
                self.validate_dataset(dataset_name)
                results[dataset_name] = 'valid'
            except Exception as e:
                logger.error(f"验证 {dataset_name} 失败: {e}")
                results[dataset_name] = f'invalid: {e}'

        # 打印结果摘要
        logger.info("\n📋 验证结果摘要:")
        for dataset_name, result in results.items():
            status_icon = "✅" if result == 'valid' else "❌"
            logger.info(f"{status_icon} {dataset_name}: {result}")

    def get_dataset_info(self, dataset_name: str):
        """获取数据集详细信息"""
        if dataset_name not in self.SUPPORTED_DATASETS:
            raise ValueError(f"Unsupported dataset: {dataset_name}")

        logger.info(f"📊 数据集信息: {dataset_name}")

        try:
            config = {
                'dataset': dataset_name,
                'data_dir': self.data_dir,
                'max_sequence_length': 128
            }

            loader = RecommendationDataLoader(config)
            info = loader.get_dataset_info()

            logger.info(f"✅ 数据集 {dataset_name} 信息:")
            logger.info(f"   描述: {info['dataset_description']}")
            logger.info(f"   用户数: {info['total_users']:,}")
            logger.info(f"   物品数: {info['total_items']:,}")
            logger.info(f"   训练用户: {info['train_users']:,}")
            logger.info(f"   验证用户: {info['valid_users']:,}")
            logger.info(f"   测试用户: {info['test_users']:,}")
            logger.info(f"   文本特征: {info['text_features']}")

            # 计算交互统计
            train_interactions = sum(len(seq) for seq in loader.train_sequences.values())
            valid_interactions = sum(len(seq) for seq in loader.valid_sequences.values())
            test_interactions = sum(len(seq) for seq in loader.test_sequences.values())

            logger.info(f"   训练交互: {train_interactions:,}")
            logger.info(f"   验证交互: {valid_interactions:,}")
            logger.info(f"   测试交互: {test_interactions:,}")
            logger.info(f"   总交互数: {train_interactions + valid_interactions + test_interactions:,}")

            # 计算平均序列长度
            if loader.train_sequences:
                avg_seq_len = train_interactions / len(loader.train_sequences)
                logger.info(f"   平均序列长度: {avg_seq_len:.2f}")

        except Exception as e:
            logger.error(f"❌ 获取数据集 {dataset_name} 信息失败: {e}")
            raise
    
    def _check_dataset_status(self, dataset_name: str) -> Dict[str, Any]:
        """检查数据集状态"""
        status = {'available': False}
        
        try:
            config = {
                'dataset': dataset_name,
                'data_dir': self.data_dir,
                'max_sequence_length': 128
            }
            
            # 创建临时加载器检查文件
            loader = RecommendationDataLoader.__new__(RecommendationDataLoader)
            loader.dataset_name = dataset_name
            loader.data_dir = self.data_dir
            
            if loader._check_data_files_exist():
                # 尝试加载获取统计信息
                try:
                    loader.max_sequence_length = 128
                    loader._load_datasets()
                    info = loader.get_dataset_info()
                    
                    status.update({
                        'available': True,
                        'users': info['total_users'],
                        'items': info['total_items'],
                        'train_interactions': sum(len(seq) for seq in loader.train_sequences.values())
                    })
                except:
                    status['available'] = True  # 文件存在但可能有问题
            
        except:
            pass
        
        return status
    
    def _get_dataset_description(self, dataset_name: str) -> str:
        """获取数据集描述"""
        descriptions = {
            'Movies_and_TV': '电影和电视节目推荐数据集',
            'Industrial_and_Scientific': '工业和科学产品推荐数据集'
        }
        return descriptions.get(dataset_name, '未知数据集')


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='数据集管理工具 - 类似LLM-SRec的命令行接口',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s --list                                    # 列出所有数据集
  %(prog)s --dataset Movies_and_TV --action download # 下载数据集
  %(prog)s --dataset Movies_and_TV --action validate # 验证数据集
  %(prog)s --dataset Movies_and_TV --action set-default # 设置为默认数据集
  %(prog)s --dataset Movies_and_TV --action clean    # 清理数据集
        """
    )
    
    parser.add_argument('--dataset', type=str, 
                       choices=['Movies_and_TV', 'Industrial_and_Scientific'],
                       help='数据集名称')
    parser.add_argument('--action', type=str,
                       choices=['download', 'validate', 'set-default', 'clean', 'info'],
                       help='执行的操作')
    parser.add_argument('--list', action='store_true',
                       help='列出所有支持的数据集')
    parser.add_argument('--all', action='store_true',
                       help='对所有数据集执行操作')
    parser.add_argument('--data_dir', type=str, default='./data',
                       help='数据目录 (默认: ./data)')
    parser.add_argument('--force', action='store_true',
                       help='强制重新下载数据集')
    parser.add_argument('--log_level', type=str, default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 创建数据集管理器
    manager = DatasetManager(args.data_dir)
    
    try:
        if args.list:
            # 列出数据集
            manager.list_datasets()

        elif args.all and args.action:
            # 对所有数据集执行操作
            if args.action == 'download':
                manager.download_all_datasets(args.force)
            elif args.action == 'validate':
                manager.validate_all_datasets()
            elif args.action == 'clean':
                manager.clean_all_datasets()
            else:
                logger.error(f"操作 '{args.action}' 不支持 --all 参数")
                sys.exit(1)

        elif args.dataset and args.action:
            # 执行指定操作
            if args.action == 'download':
                manager.download_dataset(args.dataset, args.force)
            elif args.action == 'validate':
                manager.validate_dataset(args.dataset)
            elif args.action == 'set-default':
                manager.set_default_dataset(args.dataset)
            elif args.action == 'clean':
                manager.clean_dataset(args.dataset)
            elif args.action == 'info':
                manager.get_dataset_info(args.dataset)

        else:
            # 显示帮助信息
            parser.print_help()
            logger.info("\n💡 使用示例:")
            logger.info("  python dataset_manager.py --list")
            logger.info("  python dataset_manager.py --dataset Movies_and_TV --action download")
            logger.info("  python dataset_manager.py --all --action validate")
    
    except Exception as e:
        logger.error(f"操作失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
