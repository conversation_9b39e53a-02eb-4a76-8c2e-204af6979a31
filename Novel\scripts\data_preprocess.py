"""
数据预处理脚本

基于LLM-SRec的数据预处理逻辑，支持从Amazon 2023数据集自动下载和处理：
1. 自动下载Amazon Reviews 2023数据集
2. 数据清洗和过滤
3. 构建用户-物品交互序列
4. 生成物品文本描述映射
5. 数据集分割（训练/验证/测试）
"""

import os
import sys
import pickle
import json
import gzip
import numpy as np
import logging
from tqdm import tqdm
from collections import defaultdict
import random
import argparse
from typing import Dict, List, Tuple, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from datasets import load_dataset
    HAS_DATASETS = True
except ImportError:
    HAS_DATASETS = False
    load_dataset = None

logger = logging.getLogger(__name__)


class AmazonDataPreprocessor:
    """
    Amazon数据集预处理器
    
    负责从Amazon Reviews 2023数据集下载、清洗和预处理数据
    """
    
    SUPPORTED_DATASETS = {
        'Movies_and_TV': {
            'description': '电影和电视节目推荐数据集',
            'min_interactions': 5,
            'min_users': 5
        },
        'Industrial_and_Scientific': {
            'description': '工业和科学产品推荐数据集',
            'min_interactions': 5,
            'min_users': 5
        }
    }
    
    def __init__(self, dataset_name: str, output_dir: str = "./data"):
        """
        初始化预处理器
        
        Args:
            dataset_name: 数据集名称
            output_dir: 输出目录
        """
        if dataset_name not in self.SUPPORTED_DATASETS:
            raise ValueError(f"Unsupported dataset: {dataset_name}")
        
        self.dataset_name = dataset_name
        self.output_dir = output_dir
        self.dataset_dir = os.path.join(output_dir, dataset_name)
        
        # 创建输出目录
        os.makedirs(self.dataset_dir, exist_ok=True)
        
        # 设置随机种子
        random.seed(42)
        np.random.seed(42)
        
        logger.info(f"Initialized preprocessor for {dataset_name}")
        logger.info(f"Output directory: {self.dataset_dir}")
    
    def preprocess_dataset(self, force_download: bool = False):
        """
        预处理数据集的主要流程
        
        Args:
            force_download: 是否强制重新下载数据
        """
        logger.info(f"Starting preprocessing for {self.dataset_name}")
        
        # 检查是否已存在处理好的数据
        if self._check_processed_data() and not force_download:
            logger.info("Processed data already exists. Use force_download=True to reprocess.")
            return
        
        # 步骤1: 下载原始数据
        logger.info("Step 1: Downloading raw data...")
        raw_data, meta_data = self._download_raw_data()
        
        # 步骤2: 构建物品元数据字典
        logger.info("Step 2: Building item metadata...")
        meta_dict = self._build_meta_dict(meta_data)
        
        # 步骤3: 处理用户交互数据
        logger.info("Step 3: Processing user interactions...")
        user_interactions = self._process_interactions(raw_data)
        
        # 步骤4: 数据过滤和清洗
        logger.info("Step 4: Filtering and cleaning data...")
        filtered_data = self._filter_data(user_interactions, meta_dict)
        
        # 步骤5: 数据集分割
        logger.info("Step 5: Splitting dataset...")
        train_data, valid_data, test_data = self._split_dataset(filtered_data)
        
        # 步骤6: 保存处理后的数据
        logger.info("Step 6: Saving processed data...")
        self._save_processed_data(train_data, valid_data, test_data, meta_dict)
        
        logger.info("Preprocessing completed successfully!")
    
    def _check_processed_data(self) -> bool:
        """检查是否已存在处理好的数据"""
        required_files = [
            f'{self.dataset_name}_train.txt',
            f'{self.dataset_name}_valid.txt',
            f'{self.dataset_name}_test.txt',
            'text_name_dict.json.gz'
        ]
        
        for file in required_files:
            if not os.path.exists(os.path.join(self.dataset_dir, file)):
                return False
        
        return True
    
    def _download_raw_data(self) -> Tuple[Any, Any]:
        """下载原始数据"""
        if not HAS_DATASETS:
            raise ImportError(
                "datasets library is required for automatic download. "
                "Please install it with: pip install datasets"
            )

        try:
            # 下载交互数据
            dataset = load_dataset(
                "McAuley-Lab/Amazon-Reviews-2023",
                f"5core_last_out_{self.dataset_name}"
            )

            # 下载元数据
            meta_dataset = load_dataset(
                "McAuley-Lab/Amazon-Reviews-2023",
                f"raw_meta_{self.dataset_name}"
            )

            logger.info("Raw data downloaded successfully")
            return dataset, meta_dataset

        except Exception as e:
            logger.error(f"Failed to download data: {e}")
            raise
    
    def _build_meta_dict(self, meta_dataset: Any) -> Dict[str, List[str]]:
        """构建物品元数据字典"""
        meta_dict = {}
        
        for item in tqdm(meta_dataset['full'], desc="Processing metadata"):
            if 'parent_asin' in item:
                title = item.get('title', 'Unknown Title')
                description = item.get('description', 'Empty description')
                
                # 处理描述字段
                if description is None or len(description) == 0:
                    description = 'Empty description'
                elif isinstance(description, list) and len(description) > 0:
                    description = description[0]
                
                meta_dict[item['parent_asin']] = [title, description]
        
        logger.info(f"Built metadata for {len(meta_dict)} items")
        return meta_dict
    
    def _process_interactions(self, dataset: Any) -> Dict[str, Dict[str, List[Tuple[str, float]]]]:
        """处理用户交互数据"""
        user_interactions = {
            'train': defaultdict(list),
            'valid': defaultdict(list),
            'test': defaultdict(list)
        }
        
        for split in ['train', 'valid', 'test']:
            for interaction in tqdm(dataset[split], desc=f"Processing {split} interactions"):
                user_id = interaction['user_id']
                item_id = interaction['parent_asin']
                timestamp = interaction.get('timestamp', 0)
                
                user_interactions[split][user_id].append((item_id, timestamp))
        
        # 按时间戳排序
        for split in user_interactions:
            for user_id in user_interactions[split]:
                user_interactions[split][user_id].sort(key=lambda x: x[1])
        
        return user_interactions
    
    def _filter_data(
        self, 
        user_interactions: Dict[str, Dict[str, List[Tuple[str, float]]]], 
        meta_dict: Dict[str, List[str]]
    ) -> Dict[str, Any]:
        """数据过滤和清洗"""
        config = self.SUPPORTED_DATASETS[self.dataset_name]
        min_interactions = config['min_interactions']
        
        # 统计用户和物品频次
        user_counts = defaultdict(int)
        item_counts = defaultdict(int)
        
        for split in user_interactions:
            for user_id, items in user_interactions[split].items():
                user_counts[user_id] += len(items)
                for item_id, _ in items:
                    if item_id in meta_dict:
                        item_counts[item_id] += 1
        
        # 过滤低频用户和物品
        valid_users = {u for u, c in user_counts.items() if c >= min_interactions}
        valid_items = {i for i, c in item_counts.items() if c >= min_interactions}
        
        logger.info(f"Valid users: {len(valid_users)}, Valid items: {len(valid_items)}")
        
        # 重新映射ID
        user_mapping = {user: idx + 1 for idx, user in enumerate(sorted(valid_users))}
        item_mapping = {item: idx + 1 for idx, item in enumerate(sorted(valid_items))}
        
        # 构建过滤后的数据
        filtered_data = {
            'user_mapping': user_mapping,
            'item_mapping': item_mapping,
            'interactions': {},
            'meta_dict': {item_mapping[item]: meta_dict[item] for item in valid_items}
        }
        
        for split in user_interactions:
            filtered_data['interactions'][split] = defaultdict(list)
            for user_id, items in user_interactions[split].items():
                if user_id in user_mapping:
                    mapped_user = user_mapping[user_id]
                    for item_id, timestamp in items:
                        if item_id in item_mapping:
                            mapped_item = item_mapping[item_id]
                            filtered_data['interactions'][split][mapped_user].append(mapped_item)
        
        return filtered_data
    
    def _split_dataset(self, filtered_data: Dict[str, Any]) -> Tuple[Dict, Dict, Dict]:
        """数据集分割（已经在原始数据中分割好了）"""
        return (
            filtered_data['interactions']['train'],
            filtered_data['interactions']['valid'],
            filtered_data['interactions']['test']
        )
    
    def _save_processed_data(
        self, 
        train_data: Dict, 
        valid_data: Dict, 
        test_data: Dict, 
        meta_dict: Dict
    ):
        """保存处理后的数据"""
        # 保存交互数据
        for split, data in [('train', train_data), ('valid', valid_data), ('test', test_data)]:
            file_path = os.path.join(self.dataset_dir, f'{self.dataset_name}_{split}.txt')
            with open(file_path, 'w') as f:
                for user_id, items in data.items():
                    for item_id in items:
                        f.write(f'{user_id} {item_id}\n')
            
            logger.info(f"Saved {split} data: {len(data)} users")
        
        # 保存文本字典
        text_dict = {
            'title': {item_id: meta[0] for item_id, meta in meta_dict.items()},
            'description': {item_id: meta[1] for item_id, meta in meta_dict.items()},
            'time': {}  # 保持与LLM-SRec格式兼容
        }
        
        text_dict_path = os.path.join(self.dataset_dir, 'text_name_dict.json.gz')
        with open(text_dict_path, 'wb') as f:
            pickle.dump(text_dict, f)
        
        logger.info(f"Saved text dictionary with {len(meta_dict)} items")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Amazon数据集预处理')
    parser.add_argument('--dataset', type=str, required=True,
                       choices=['Movies_and_TV', 'Industrial_and_Scientific'],
                       help='数据集名称')
    parser.add_argument('--output_dir', type=str, default='./data',
                       help='输出目录')
    parser.add_argument('--force_download', action='store_true',
                       help='强制重新下载数据')
    parser.add_argument('--log_level', type=str, default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建预处理器并运行
    preprocessor = AmazonDataPreprocessor(args.dataset, args.output_dir)
    preprocessor.preprocess_dataset(args.force_download)


if __name__ == "__main__":
    main()
