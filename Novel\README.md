# Novel

Enhanced collaborative recommendation system based on LLM-SRec.

## Quick Start

```bash
# Setup data
python setup_data.py

# Train on Movies_and_TV
python main.py --dataset Movies_and_TV --train

# Train on Industrial_and_Scientific
python main.py --dataset Industrial_and_Scientific --train
```

## Requirements

```bash
pip install torch numpy tqdm
```

## Data Setup

```bash
# Setup all datasets (copies from LLM-SRec if available)
python setup_data.py

# Setup specific dataset
python setup_data.py --dataset Movies_and_TV
```

## Usage

### Training
```bash
# Basic training
python main.py --dataset Movies_and_TV --train

# With custom parameters
python main.py --dataset Movies_and_TV --train --batch_size 64 --num_epochs 100 --lr 0.001

# Train on different dataset
python main.py --dataset Industrial_and_Scientific --train
```

### Evaluation
```bash
python main.py --dataset Movies_and_TV --eval
```

## Project Structure

```
Novel/
├── main.py           # Main entry point
├── train_model.py    # Training functions
├── utils.py          # Utility functions
├── setup_data.py     # Data setup script
└── data/             # Data directory
    ├── Movies_and_TV/
    └── Industrial_and_Scientific/
```

## Arguments

```
--dataset          Dataset name (Movies_and_TV, Industrial_and_Scientific)
--train            Run training
--eval             Run evaluation
--batch_size       Batch size (default: 32)
--num_epochs       Number of epochs (default: 50)
--lr               Learning rate (default: 0.001)
--maxlen           Max sequence length (default: 128)
--device           GPU device (default: 0)
--save_dir         Save directory (default: results)
```

## License

MIT
