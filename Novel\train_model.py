"""
Novel: Simplified training script
Core training and evaluation functions
"""

import os
import time
import torch
import torch.nn as nn
from tqdm import tqdm

from utils import *

class SimpleRecModel(nn.Module):
    """Simple recommendation model for demonstration"""
    
    def __init__(self, num_items, hidden_size=64, max_len=128):
        super().__init__()
        self.num_items = num_items
        self.hidden_size = hidden_size
        self.max_len = max_len
        
        # Embedding layers
        self.item_embedding = nn.Embedding(num_items + 1, hidden_size, padding_idx=0)
        self.position_embedding = nn.Embedding(max_len, hidden_size)
        
        # Transformer layer
        self.transformer = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(
                d_model=hidden_size,
                nhead=4,
                dim_feedforward=hidden_size * 2,
                dropout=0.1,
                batch_first=True
            ),
            num_layers=2
        )
        
        # Output layer
        self.output_layer = nn.Linear(hidden_size, num_items + 1)
        
    def forward(self, sequences, lengths):
        batch_size, seq_len = sequences.size()
        
        # Item embeddings
        item_emb = self.item_embedding(sequences)
        
        # Position embeddings
        positions = torch.arange(seq_len, device=sequences.device).unsqueeze(0).expand(batch_size, -1)
        pos_emb = self.position_embedding(positions)
        
        # Combined embeddings
        embeddings = item_emb + pos_emb
        
        # Create attention mask
        mask = (sequences == 0)
        
        # Transformer
        output = self.transformer(embeddings, src_key_padding_mask=mask)
        
        # Get last non-padding position for each sequence
        last_positions = lengths - 1
        last_outputs = output[torch.arange(batch_size), last_positions]
        
        # Predictions
        logits = self.output_layer(last_outputs)
        
        return logits

def train_model(args):
    """Main training function"""
    print(f"🎯 Training Novel model on {args.dataset}")
    
    # Set seed
    set_seed(args.seed)
    
    # Load data
    try:
        train_data, valid_data, test_data, text_dict = load_dataset(args.dataset)
    except FileNotFoundError:
        return
    
    # Get dataset stats
    stats = get_dataset_stats(train_data, valid_data, test_data)
    print(f"📊 Dataset stats: {stats['num_users']} users, {stats['num_items']} items")
    
    # Create data loaders
    train_loader = create_data_loader(train_data, args.batch_size, args.maxlen, shuffle=True)
    valid_loader = create_data_loader(valid_data, args.batch_size, args.maxlen, shuffle=False)
    
    # Initialize model
    model = SimpleRecModel(stats['num_items'], max_len=args.maxlen).to(args.device)
    optimizer = torch.optim.Adam(model.parameters(), lr=args.lr)
    criterion = nn.CrossEntropyLoss(ignore_index=0)
    
    print(f"🤖 Model initialized with {sum(p.numel() for p in model.parameters())} parameters")
    
    # Training loop
    best_ndcg = 0.0
    ensure_dir(args.save_dir)
    
    for epoch in range(args.num_epochs):
        # Training
        model.train()
        train_loss = 0.0
        train_steps = 0
        
        pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{args.num_epochs}')
        for batch in pbar:
            sequences = batch['sequence'].to(args.device)
            lengths = batch['length'].to(args.device)
            
            # Create targets (next item prediction)
            targets = torch.zeros_like(sequences[:, 0])  # Simplified target
            
            # Forward pass
            logits = model(sequences, lengths)
            loss = criterion(logits, targets)
            
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_steps += 1
            
            pbar.set_postfix({'loss': f'{loss.item():.4f}'})
        
        avg_train_loss = train_loss / train_steps
        
        # Validation
        if (epoch + 1) % 5 == 0:  # Validate every 5 epochs
            val_metrics = evaluate_model(model, valid_loader, args.device)
            
            print(f"Epoch {epoch+1}:")
            print(f"  Train Loss: {avg_train_loss:.4f}")
            print_metrics(val_metrics, "  Validation")
            
            # Save best model
            if val_metrics['ndcg@10'] > best_ndcg:
                best_ndcg = val_metrics['ndcg@10']
                model_path = os.path.join(args.save_dir, f'best_model_{args.dataset}.pt')
                torch.save(model.state_dict(), model_path)
                print(f"  💾 New best model saved (NDCG@10: {best_ndcg:.4f})")
    
    print(f"🎉 Training completed! Best NDCG@10: {best_ndcg:.4f}")
    
    # Final evaluation on test set
    print("\n🔍 Final evaluation on test set...")
    test_loader = create_data_loader(test_data, args.batch_size, args.maxlen, shuffle=False)
    
    # Load best model if it exists
    model_path = os.path.join(args.save_dir, f'best_model_{args.dataset}.pt')
    if os.path.exists(model_path):
        model.load_state_dict(torch.load(model_path))
        print(f"📦 Loaded best model from {model_path}")
    else:
        print("⚠️ No saved model found, using current model for evaluation")

    test_metrics = evaluate_model(model, test_loader, args.device)
    
    print("📊 Final Test Results:")
    print_metrics(test_metrics, "  Test")
    
    # Save results
    args_dict = vars(args).copy()
    args_dict['device'] = str(args_dict['device'])  # Convert device to string

    results = {
        'dataset': args.dataset,
        'best_val_ndcg': best_ndcg,
        'test_metrics': test_metrics,
        'args': args_dict
    }
    
    import json
    with open(os.path.join(args.save_dir, f'results_{args.dataset}.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"💾 Results saved to {args.save_dir}/results_{args.dataset}.json")

def evaluate_model(model, data_loader, device):
    """Evaluate model on given data loader"""
    model.eval()
    all_metrics = []
    
    with torch.no_grad():
        for batch in tqdm(data_loader, desc='Evaluating'):
            sequences = batch['sequence'].to(device)
            lengths = batch['length'].to(device)
            
            # Forward pass
            logits = model(sequences, lengths)
            
            # Create dummy targets for metric calculation
            targets = torch.zeros_like(logits)
            targets[:, 1] = 1  # Simplified: assume item 1 is always relevant
            
            # Calculate metrics
            metrics = calculate_metrics(logits, targets, k=10)
            all_metrics.append(metrics)
    
    # Average metrics
    avg_metrics = {}
    for key in all_metrics[0].keys():
        avg_metrics[key] = sum(m[key] for m in all_metrics) / len(all_metrics)
    
    return avg_metrics

def eval_model(args):
    """Evaluation only function"""
    print(f"🔍 Evaluating Novel model on {args.dataset}")
    
    # Load data
    try:
        _, _, test_data, _ = load_dataset(args.dataset)
    except FileNotFoundError:
        return
    
    # Load model
    model_path = os.path.join(args.save_dir, f'best_model_{args.dataset}.pt')
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        print("💡 Please train the model first with --train")
        return
    
    # Get dataset stats for model initialization
    stats = get_dataset_stats({}, {}, test_data)
    
    # Initialize and load model
    model = SimpleRecModel(stats['num_items'], max_len=args.maxlen).to(args.device)
    model.load_state_dict(torch.load(model_path))
    
    # Create test loader
    test_loader = create_data_loader(test_data, args.batch_size, args.maxlen, shuffle=False)
    
    # Evaluate
    test_metrics = evaluate_model(model, test_loader, args.device)
    
    print("📊 Test Results:")
    print_metrics(test_metrics, "  Test")
