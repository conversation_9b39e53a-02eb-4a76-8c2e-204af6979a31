#!/usr/bin/env python3
"""
LLM-SRec训练启动脚本

提供类似LLM-SRec的命令行接口，支持：
1. 通过--dataset参数一键切换数据集
2. 自动检测和下载缺失的数据集
3. 灵活的训练参数配置
4. 多数据集批量训练

使用示例：
    python train.py --dataset Movies_and_TV
    python train.py --dataset Industrial_and_Scientific --epochs 100
    python train.py --dataset Movies_and_TV --batch_size 64 --learning_rate 0.001
"""

import os
import sys
import argparse
import logging
import subprocess
from typing import List, Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

logger = logging.getLogger(__name__)


class TrainingLauncher:
    """训练启动器"""
    
    SUPPORTED_DATASETS = ['Movies_and_TV', 'Industrial_and_Scientific']
    
    def __init__(self):
        """初始化训练启动器"""
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.trainer_script = os.path.join(self.script_dir, 'training', 'collaborative_trainer.py')
        self.dataset_manager = os.path.join(self.script_dir, 'dataset_manager.py')
    
    def check_dataset_availability(self, dataset_name: str) -> bool:
        """检查数据集是否可用"""
        try:
            # 使用dataset_manager检查数据集状态
            result = subprocess.run([
                sys.executable, self.dataset_manager,
                '--dataset', dataset_name,
                '--action', 'validate'
            ], capture_output=True, text=True, cwd=self.script_dir)
            
            return result.returncode == 0
            
        except Exception as e:
            logger.warning(f"检查数据集状态失败: {e}")
            return False
    
    def download_dataset(self, dataset_name: str, force: bool = False):
        """下载数据集"""
        logger.info(f"📥 准备下载数据集: {dataset_name}")
        
        try:
            cmd = [
                sys.executable, self.dataset_manager,
                '--dataset', dataset_name,
                '--action', 'download'
            ]
            
            if force:
                cmd.append('--force')
            
            result = subprocess.run(cmd, cwd=self.script_dir)
            
            if result.returncode != 0:
                raise RuntimeError(f"数据集下载失败，退出码: {result.returncode}")
            
            logger.info(f"✅ 数据集 {dataset_name} 下载完成")
            
        except Exception as e:
            logger.error(f"❌ 下载数据集 {dataset_name} 失败: {e}")
            raise
    
    def launch_training(self, args: argparse.Namespace):
        """启动训练"""
        # 检查数据集
        if args.dataset:
            if not self.check_dataset_availability(args.dataset):
                logger.info(f"数据集 {args.dataset} 不可用，开始自动下载...")
                self.download_dataset(args.dataset, args.force_download)
        
        # 构建训练命令
        cmd = [sys.executable, self.trainer_script]
        
        # 添加参数
        if args.dataset:
            cmd.extend(['--dataset', args.dataset])
        if args.config:
            cmd.extend(['--config', args.config])
        if args.epochs:
            cmd.extend(['--epochs', str(args.epochs)])
        if args.batch_size:
            cmd.extend(['--batch_size', str(args.batch_size)])
        if args.learning_rate:
            cmd.extend(['--learning_rate', str(args.learning_rate)])
        if args.output_dir:
            cmd.extend(['--output_dir', args.output_dir])
        if args.device:
            cmd.extend(['--device', args.device])
        if args.log_level:
            cmd.extend(['--log_level', args.log_level])
        if args.no_wandb:
            cmd.append('--no_wandb')
        if args.seed:
            cmd.extend(['--seed', str(args.seed)])
        
        logger.info(f"🚀 启动训练: {' '.join(cmd)}")
        
        try:
            # 启动训练
            result = subprocess.run(cmd, cwd=self.script_dir)
            
            if result.returncode == 0:
                logger.info("🎉 训练完成!")
            else:
                logger.error(f"❌ 训练失败，退出码: {result.returncode}")
                sys.exit(result.returncode)
                
        except KeyboardInterrupt:
            logger.info("训练被用户中断")
            sys.exit(0)
        except Exception as e:
            logger.error(f"启动训练失败: {e}")
            sys.exit(1)
    
    def batch_training(self, datasets: List[str], args: argparse.Namespace):
        """批量训练多个数据集"""
        logger.info(f"🔄 开始批量训练: {datasets}")
        
        results = {}
        
        for dataset_name in datasets:
            logger.info(f"\n{'='*60}")
            logger.info(f"开始训练数据集: {dataset_name}")
            logger.info(f"{'='*60}")
            
            try:
                # 更新参数
                args.dataset = dataset_name
                if args.output_dir:
                    # 为每个数据集创建单独的输出目录
                    args.output_dir = f"{args.output_dir}_{dataset_name}"
                
                # 启动训练
                self.launch_training(args)
                results[dataset_name] = 'success'
                
            except Exception as e:
                logger.error(f"❌ 数据集 {dataset_name} 训练失败: {e}")
                results[dataset_name] = f'failed: {e}'
        
        # 打印批量训练结果
        logger.info(f"\n{'='*60}")
        logger.info("批量训练结果:")
        logger.info(f"{'='*60}")
        
        for dataset_name, result in results.items():
            status_icon = "✅" if result == 'success' else "❌"
            logger.info(f"{status_icon} {dataset_name}: {result}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='LLM-SRec训练启动器 - 类似LLM-SRec的命令行接口',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s --dataset Movies_and_TV                     # 训练Movies_and_TV数据集
  %(prog)s --dataset Industrial_and_Scientific        # 训练Industrial_and_Scientific数据集
  %(prog)s --dataset Movies_and_TV --epochs 100       # 指定训练轮数
  %(prog)s --batch_all                                # 批量训练所有数据集
  %(prog)s --dataset Movies_and_TV --force_download   # 强制重新下载数据集
        """
    )
    
    # 数据集相关参数
    parser.add_argument('--dataset', type=str,
                       choices=['Movies_and_TV', 'Industrial_and_Scientific'],
                       help='数据集名称')
    parser.add_argument('--batch_all', action='store_true',
                       help='批量训练所有支持的数据集')
    parser.add_argument('--force_download', action='store_true',
                       help='强制重新下载数据集')
    
    # 训练参数
    parser.add_argument('--config', type=str, default='config/collaborative_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--epochs', type=int,
                       help='训练轮数')
    parser.add_argument('--batch_size', type=int,
                       help='批次大小')
    parser.add_argument('--learning_rate', type=float,
                       help='学习率')
    parser.add_argument('--output_dir', type=str,
                       help='输出目录')
    parser.add_argument('--device', type=str,
                       help='设备 (cuda:0, cpu等)')
    parser.add_argument('--seed', type=int,
                       help='随机种子')
    
    # 其他参数
    parser.add_argument('--log_level', type=str, default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    parser.add_argument('--no_wandb', action='store_true',
                       help='禁用wandb日志记录')
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 创建训练启动器
    launcher = TrainingLauncher()
    
    try:
        if args.batch_all:
            # 批量训练所有数据集
            launcher.batch_training(launcher.SUPPORTED_DATASETS, args)
        elif args.dataset:
            # 训练指定数据集
            launcher.launch_training(args)
        else:
            # 显示帮助信息
            parser.print_help()
            logger.info("\n💡 提示: 使用 --dataset 参数指定数据集，或使用 --batch_all 批量训练所有数据集")
    
    except Exception as e:
        logger.error(f"启动器运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
