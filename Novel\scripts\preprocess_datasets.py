"""
批量数据预处理脚本

便捷地预处理多个数据集，支持：
1. 批量处理Movies_and_TV和Industrial_and_Scientific数据集
2. 数据验证和统计
3. 配置文件自动更新
"""

import os
import sys
import logging
import argparse
from typing import List, Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scripts.data_preprocess import AmazonDataPreprocessor
from utils.data_utils import RecommendationDataLoader

logger = logging.getLogger(__name__)


class BatchDataPreprocessor:
    """批量数据预处理器"""
    
    def __init__(self, output_dir: str = "./data"):
        """
        初始化批量预处理器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        self.datasets = ['Movies_and_TV', 'Industrial_and_Scientific']
        
    def preprocess_all_datasets(self, force_download: bool = False):
        """
        预处理所有支持的数据集
        
        Args:
            force_download: 是否强制重新下载
        """
        logger.info("Starting batch preprocessing for all datasets")
        
        results = {}
        
        for dataset_name in self.datasets:
            logger.info(f"\n{'='*50}")
            logger.info(f"Processing dataset: {dataset_name}")
            logger.info(f"{'='*50}")
            
            try:
                # 创建预处理器
                preprocessor = AmazonDataPreprocessor(dataset_name, self.output_dir)
                
                # 预处理数据
                preprocessor.preprocess_dataset(force_download)
                
                # 验证数据
                stats = self._validate_dataset(dataset_name)
                results[dataset_name] = {'status': 'success', 'stats': stats}
                
                logger.info(f"✅ {dataset_name} processed successfully")
                
            except Exception as e:
                logger.error(f"❌ Failed to process {dataset_name}: {e}")
                results[dataset_name] = {'status': 'failed', 'error': str(e)}
        
        # 打印总结
        self._print_summary(results)
        
        return results
    
    def _validate_dataset(self, dataset_name: str) -> Dict[str, Any]:
        """
        验证数据集
        
        Args:
            dataset_name: 数据集名称
            
        Returns:
            数据集统计信息
        """
        try:
            # 创建数据配置
            config = {
                'dataset': dataset_name,
                'data_dir': self.output_dir,
                'max_sequence_length': 128
            }
            
            # 加载数据
            data_loader = RecommendationDataLoader(config)
            
            # 获取统计信息
            stats = data_loader.get_dataset_info()
            
            logger.info(f"Dataset validation successful for {dataset_name}")
            logger.info(f"  Total users: {stats['total_users']}")
            logger.info(f"  Total items: {stats['total_items']}")
            logger.info(f"  Train users: {stats['train_users']}")
            logger.info(f"  Valid users: {stats['valid_users']}")
            logger.info(f"  Test users: {stats['test_users']}")
            
            return stats
            
        except Exception as e:
            logger.error(f"Dataset validation failed for {dataset_name}: {e}")
            return {'error': str(e)}
    
    def _print_summary(self, results: Dict[str, Dict[str, Any]]):
        """打印处理结果总结"""
        logger.info(f"\n{'='*60}")
        logger.info("BATCH PREPROCESSING SUMMARY")
        logger.info(f"{'='*60}")
        
        successful = []
        failed = []
        
        for dataset_name, result in results.items():
            if result['status'] == 'success':
                successful.append(dataset_name)
                stats = result['stats']
                logger.info(f"✅ {dataset_name}:")
                logger.info(f"   Users: {stats.get('total_users', 'N/A')}")
                logger.info(f"   Items: {stats.get('total_items', 'N/A')}")
                logger.info(f"   Train/Valid/Test: {stats.get('train_users', 'N/A')}/"
                           f"{stats.get('valid_users', 'N/A')}/{stats.get('test_users', 'N/A')}")
            else:
                failed.append(dataset_name)
                logger.error(f"❌ {dataset_name}: {result['error']}")
        
        logger.info(f"\nSUCCESSFUL: {len(successful)}/{len(results)} datasets")
        logger.info(f"FAILED: {len(failed)}/{len(results)} datasets")
        
        if successful:
            logger.info(f"Successfully processed: {', '.join(successful)}")
        
        if failed:
            logger.error(f"Failed to process: {', '.join(failed)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量数据预处理')
    parser.add_argument('--output_dir', type=str, default='./data',
                       help='输出目录')
    parser.add_argument('--force_download', action='store_true',
                       help='强制重新下载数据')
    parser.add_argument('--datasets', nargs='+', 
                       choices=['Movies_and_TV', 'Industrial_and_Scientific'],
                       help='指定要处理的数据集（默认处理所有）')
    parser.add_argument('--log_level', type=str, default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建批量预处理器
    batch_preprocessor = BatchDataPreprocessor(args.output_dir)
    
    # 如果指定了特定数据集，只处理这些数据集
    if args.datasets:
        batch_preprocessor.datasets = args.datasets
    
    # 运行批量预处理
    results = batch_preprocessor.preprocess_all_datasets(args.force_download)
    
    # 检查是否有失败的数据集
    failed_count = sum(1 for r in results.values() if r['status'] == 'failed')
    if failed_count > 0:
        sys.exit(1)


if __name__ == "__main__":
    main()
