#!/usr/bin/env python3
"""
命令行功能演示脚本

展示Novel项目的完整命令行功能，包括：
1. 数据集管理器功能演示
2. 训练启动器功能演示
3. 数据集切换和自动下载演示
4. 参数传递和配置覆盖演示
"""

import os
import sys
import subprocess
import logging
import time
from typing import List, Dict, Any

logger = logging.getLogger(__name__)


class CLIFeaturesDemo:
    """命令行功能演示器"""
    
    def __init__(self):
        """初始化演示器"""
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.datasets = ['Movies_and_TV', 'Industrial_and_Scientific']
    
    def run_command(self, cmd: List[str], description: str) -> bool:
        """运行命令并显示结果"""
        logger.info(f"🔧 {description}")
        logger.info(f"   命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd, 
                cwd=self.script_dir, 
                capture_output=True, 
                text=True, 
                timeout=60
            )
            
            if result.returncode == 0:
                logger.info(f"   ✅ 成功")
                # 显示关键输出行
                output_lines = result.stdout.strip().split('\n')
                for line in output_lines[-3:]:  # 显示最后3行
                    if line.strip():
                        logger.info(f"   📄 {line.strip()}")
                return True
            else:
                logger.error(f"   ❌ 失败 (退出码: {result.returncode})")
                if result.stderr:
                    logger.error(f"   错误: {result.stderr.strip()}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"   ⏰ 超时")
            return False
        except Exception as e:
            logger.error(f"   💥 异常: {e}")
            return False
    
    def demo_dataset_manager(self):
        """演示数据集管理器功能"""
        logger.info("📋 数据集管理器功能演示")
        logger.info("=" * 50)
        
        demos = [
            {
                'cmd': ['python', 'dataset_manager.py', '--list'],
                'desc': '列出所有支持的数据集'
            },
            {
                'cmd': ['python', 'dataset_manager.py', '--dataset', 'Movies_and_TV', '--action', 'validate'],
                'desc': '验证Movies_and_TV数据集'
            },
            {
                'cmd': ['python', 'dataset_manager.py', '--dataset', 'Industrial_and_Scientific', '--action', 'info'],
                'desc': '获取Industrial_and_Scientific数据集详细信息'
            },
            {
                'cmd': ['python', 'dataset_manager.py', '--all', '--action', 'validate'],
                'desc': '批量验证所有数据集'
            }
        ]
        
        results = []
        for demo in demos:
            success = self.run_command(demo['cmd'], demo['desc'])
            results.append(success)
            time.sleep(1)  # 短暂延迟
        
        return results
    
    def demo_training_launcher(self):
        """演示训练启动器功能"""
        logger.info("\n🚀 训练启动器功能演示")
        logger.info("=" * 50)
        
        demos = [
            {
                'cmd': ['python', 'train.py', '--help'],
                'desc': '显示训练启动器帮助信息'
            },
            {
                'cmd': ['python', 'training/collaborative_trainer.py', '--help'],
                'desc': '显示训练脚本帮助信息'
            }
        ]
        
        results = []
        for demo in demos:
            success = self.run_command(demo['cmd'], demo['desc'])
            results.append(success)
            time.sleep(1)
        
        return results
    
    def demo_dataset_switching(self):
        """演示数据集切换功能"""
        logger.info("\n🔄 数据集切换功能演示")
        logger.info("=" * 50)
        
        # 演示配置文件更新
        demos = [
            {
                'cmd': ['python', 'dataset_manager.py', '--dataset', 'Industrial_and_Scientific', '--action', 'set-default'],
                'desc': '设置Industrial_and_Scientific为默认数据集'
            },
            {
                'cmd': ['python', '-c', 
                       "import yaml; "
                       "with open('config/collaborative_config.yaml', 'r', encoding='utf-8') as f: config = yaml.safe_load(f); "
                       "print(f'当前默认数据集: {config[\"data\"][\"dataset\"]}')"],
                'desc': '验证配置文件已更新'
            },
            {
                'cmd': ['python', 'dataset_manager.py', '--dataset', 'Movies_and_TV', '--action', 'set-default'],
                'desc': '恢复Movies_and_TV为默认数据集'
            }
        ]
        
        results = []
        for demo in demos:
            success = self.run_command(demo['cmd'], demo['desc'])
            results.append(success)
            time.sleep(1)
        
        return results
    
    def demo_cli_testing(self):
        """演示CLI测试功能"""
        logger.info("\n🧪 CLI测试功能演示")
        logger.info("=" * 50)
        
        demos = [
            {
                'cmd': ['python', 'test_cli.py'],
                'desc': '运行完整的CLI功能测试'
            }
        ]
        
        results = []
        for demo in demos:
            success = self.run_command(demo['cmd'], demo['desc'])
            results.append(success)
        
        return results
    
    def demo_advanced_features(self):
        """演示高级功能"""
        logger.info("\n⚡ 高级功能演示")
        logger.info("=" * 50)
        
        # 演示参数传递和数据加载
        demos = [
            {
                'cmd': ['python', '-c', '''
import sys
sys.path.append(".")
from utils.data_utils import RecommendationDataLoader

# 演示动态数据集切换
for dataset in ["Movies_and_TV", "Industrial_and_Scientific"]:
    config = {"dataset": dataset, "data_dir": "./data", "max_sequence_length": 128}
    try:
        loader = RecommendationDataLoader(config)
        info = loader.get_dataset_info()
        print(f"✅ {dataset}: {info['total_users']:,} 用户, {info['total_items']:,} 物品")
    except Exception as e:
        print(f"❌ {dataset}: {e}")
                '''],
                'desc': '演示程序化数据集切换'
            }
        ]
        
        results = []
        for demo in demos:
            success = self.run_command(demo['cmd'], demo['desc'])
            results.append(success)
        
        return results
    
    def run_full_demo(self):
        """运行完整演示"""
        logger.info("🎬 Novel项目命令行功能完整演示")
        logger.info("=" * 60)
        logger.info("展示类似LLM-SRec的命令行接口功能")
        logger.info("=" * 60)
        
        all_results = []
        
        # 1. 数据集管理器演示
        manager_results = self.demo_dataset_manager()
        all_results.extend(manager_results)
        
        # 2. 训练启动器演示
        launcher_results = self.demo_training_launcher()
        all_results.extend(launcher_results)
        
        # 3. 数据集切换演示
        switching_results = self.demo_dataset_switching()
        all_results.extend(switching_results)
        
        # 4. CLI测试演示
        testing_results = self.demo_cli_testing()
        all_results.extend(testing_results)
        
        # 5. 高级功能演示
        advanced_results = self.demo_advanced_features()
        all_results.extend(advanced_results)
        
        # 打印演示结果摘要
        self._print_demo_summary(all_results)
        
        return all_results
    
    def _print_demo_summary(self, results: List[bool]):
        """打印演示结果摘要"""
        logger.info("\n" + "=" * 60)
        logger.info("🎯 演示结果摘要")
        logger.info("=" * 60)
        
        total_demos = len(results)
        successful_demos = sum(results)
        
        logger.info(f"📊 总演示数: {total_demos}")
        logger.info(f"✅ 成功演示: {successful_demos}")
        logger.info(f"❌ 失败演示: {total_demos - successful_demos}")
        logger.info(f"📈 成功率: {successful_demos/total_demos*100:.1f}%")
        
        if successful_demos == total_demos:
            logger.info("\n🎉 所有演示都成功完成!")
            logger.info("✨ Novel项目现在具有完整的LLM-SRec风格命令行接口!")
            logger.info("\n💡 主要功能:")
            logger.info("   • 一键数据集切换: --dataset 参数")
            logger.info("   • 自动数据下载: 缺失数据集自动处理")
            logger.info("   • 灵活参数配置: 支持所有训练参数")
            logger.info("   • 批量操作: 支持多数据集批量处理")
            logger.info("   • 数据集管理: 完整的CRUD操作")
            logger.info("   • 错误处理: 详细的错误信息和恢复建议")
            
            logger.info("\n🚀 快速开始:")
            logger.info("   python train.py --dataset Movies_and_TV")
            logger.info("   python train.py --dataset Industrial_and_Scientific")
            logger.info("   python dataset_manager.py --list")
            
        else:
            logger.warning(f"\n⚠️ {total_demos - successful_demos} 个演示失败")
            logger.info("请检查错误信息并修复相关问题")


def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 创建演示器并运行演示
    demo = CLIFeaturesDemo()
    results = demo.run_full_demo()
    
    # 检查是否有失败的演示
    if not all(results):
        sys.exit(1)


if __name__ == "__main__":
    main()
