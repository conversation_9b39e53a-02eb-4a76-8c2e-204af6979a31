#!/usr/bin/env python3
"""
Novel: Simple Demo
Demonstrate the simplified LLM-SRec-like interface
"""

import subprocess
import sys

def run_command(cmd, description):
    """Run a command and show the result"""
    print(f"\n🔧 {description}")
    print(f"💻 Command: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("✅ Success!")
            # Show last few lines of output
            lines = result.stdout.strip().split('\n')
            for line in lines[-5:]:
                if line.strip():
                    print(f"   {line}")
        else:
            print("❌ Failed!")
            if result.stderr:
                print(f"   Error: {result.stderr.strip()}")
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("⏰ Timeout!")
        return False
    except Exception as e:
        print(f"💥 Exception: {e}")
        return False

def main():
    print("🎬 Novel: Simple Demo")
    print("=" * 50)
    print("Demonstrating LLM-SRec-like simplicity")
    print("=" * 50)
    
    # Demo steps
    steps = [
        {
            'cmd': ['python', 'setup_data.py'],
            'desc': 'Setup datasets (like LLM-SRec data preparation)'
        },
        {
            'cmd': ['python', 'main.py', '--help'],
            'desc': 'Show help (simple interface like LLM-SRec)'
        },
        {
            'cmd': ['python', 'main.py', '--dataset', 'Movies_and_TV', '--train', '--num_epochs', '1'],
            'desc': 'Quick training demo on Movies_and_TV'
        },
        {
            'cmd': ['python', 'main.py', '--dataset', 'Industrial_and_Scientific', '--train', '--num_epochs', '1'],
            'desc': 'Quick training demo on Industrial_and_Scientific'
        }
    ]
    
    success_count = 0
    for i, step in enumerate(steps, 1):
        print(f"\n{'='*20} Step {i}/{len(steps)} {'='*20}")
        if run_command(step['cmd'], step['desc']):
            success_count += 1
    
    print(f"\n{'='*50}")
    print(f"🎯 Demo Results: {success_count}/{len(steps)} steps successful")
    
    if success_count == len(steps):
        print("🎉 All demos successful!")
        print("\n✨ Novel is now as simple as LLM-SRec:")
        print("   python main.py --dataset Movies_and_TV --train")
        print("   python main.py --dataset Industrial_and_Scientific --train")
        print("\n📊 Key simplifications:")
        print("   • Single main.py entry point")
        print("   • Simple --dataset parameter")
        print("   • Automatic data setup")
        print("   • Minimal dependencies")
        print("   • Clean project structure")
    else:
        print("⚠️ Some demos failed, but core functionality should work")
    
    print(f"\n📁 Project structure (simplified):")
    print("   Novel/")
    print("   ├── main.py          # Main entry (like LLM-SRec)")
    print("   ├── train_model.py   # Training functions")
    print("   ├── utils.py         # Utilities")
    print("   ├── setup_data.py    # Data setup")
    print("   └── data/            # Data directory")

if __name__ == "__main__":
    main()
