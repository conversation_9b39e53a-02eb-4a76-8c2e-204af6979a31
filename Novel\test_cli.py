#!/usr/bin/env python3
"""
命令行功能测试脚本

测试所有命令行功能，包括：
1. 数据集管理器功能
2. 训练启动器功能
3. 数据集切换和自动下载
4. 参数传递和配置覆盖
"""

import os
import sys
import argparse
import logging
import yaml
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.data_utils import RecommendationDataLoader

logger = logging.getLogger(__name__)


class CLITester:
    """命令行功能测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.datasets = ['Movies_and_TV', 'Industrial_and_Scientific']
    
    def test_dataset_loading_with_args(self, dataset_name: str):
        """测试通过参数加载数据集"""
        logger.info(f"🧪 测试数据集加载: {dataset_name}")
        
        try:
            config = {
                'dataset': dataset_name,
                'data_dir': './data',
                'max_sequence_length': 128
            }
            
            # 模拟命令行参数传递
            loader = RecommendationDataLoader(config)
            
            # 获取数据集信息
            info = loader.get_dataset_info()
            
            logger.info(f"✅ 数据集 {dataset_name} 加载成功!")
            logger.info(f"   用户数: {info['total_users']:,}")
            logger.info(f"   物品数: {info['total_items']:,}")
            
            # 测试数据加载器
            train_loader = loader.get_train_loader(batch_size=8, shuffle=True)
            logger.info(f"   训练批次数: {len(train_loader):,}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据集 {dataset_name} 加载失败: {e}")
            return False
    
    def test_config_override(self, dataset_name: str):
        """测试配置覆盖功能"""
        logger.info(f"🧪 测试配置覆盖: {dataset_name}")
        
        try:
            # 加载原始配置
            config_path = os.path.join(self.script_dir, 'config', 'collaborative_config.yaml')
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 模拟命令行参数覆盖
            original_dataset = config['data']['dataset']
            config['data']['dataset'] = dataset_name
            
            # 根据数据集调整模型配置
            if 'dataset_specific' in config['small_model']:
                dataset_config = config['small_model']['dataset_specific'].get(dataset_name, {})
                for key, value in dataset_config.items():
                    config['small_model'][key] = value
            
            logger.info(f"✅ 配置覆盖成功!")
            logger.info(f"   原始数据集: {original_dataset}")
            logger.info(f"   新数据集: {config['data']['dataset']}")
            logger.info(f"   物品数: {config['small_model'].get('item_num', 'auto')}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 配置覆盖失败: {e}")
            return False
    
    def test_dataset_switching(self):
        """测试数据集切换功能"""
        logger.info("🧪 测试数据集切换功能")
        
        results = {}
        
        for dataset_name in self.datasets:
            logger.info(f"\n🔄 切换到数据集: {dataset_name}")
            
            # 测试数据加载
            load_success = self.test_dataset_loading_with_args(dataset_name)
            
            # 测试配置覆盖
            config_success = self.test_config_override(dataset_name)
            
            results[dataset_name] = load_success and config_success
        
        return results
    
    def test_parameter_validation(self):
        """测试参数验证功能"""
        logger.info("🧪 测试参数验证功能")
        
        test_cases = [
            {
                'name': '有效数据集',
                'dataset': 'Movies_and_TV',
                'should_pass': True
            },
            {
                'name': '无效数据集',
                'dataset': 'Invalid_Dataset',
                'should_pass': False
            }
        ]
        
        results = {}
        
        for test_case in test_cases:
            logger.info(f"   测试: {test_case['name']}")
            
            try:
                if test_case['dataset'] not in self.datasets:
                    # 应该抛出异常
                    if not test_case['should_pass']:
                        logger.info(f"   ✅ 正确拒绝无效数据集: {test_case['dataset']}")
                        results[test_case['name']] = True
                    else:
                        logger.error(f"   ❌ 应该接受但被拒绝: {test_case['dataset']}")
                        results[test_case['name']] = False
                else:
                    # 应该成功
                    if test_case['should_pass']:
                        logger.info(f"   ✅ 正确接受有效数据集: {test_case['dataset']}")
                        results[test_case['name']] = True
                    else:
                        logger.error(f"   ❌ 应该拒绝但被接受: {test_case['dataset']}")
                        results[test_case['name']] = False
                        
            except Exception as e:
                if not test_case['should_pass']:
                    logger.info(f"   ✅ 正确抛出异常: {e}")
                    results[test_case['name']] = True
                else:
                    logger.error(f"   ❌ 意外异常: {e}")
                    results[test_case['name']] = False
        
        return results
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始命令行功能测试")
        logger.info("=" * 60)
        
        all_results = {}
        
        # 1. 测试数据集切换
        logger.info("\n1️⃣ 数据集切换测试")
        switching_results = self.test_dataset_switching()
        all_results['dataset_switching'] = switching_results
        
        # 2. 测试参数验证
        logger.info("\n2️⃣ 参数验证测试")
        validation_results = self.test_parameter_validation()
        all_results['parameter_validation'] = validation_results
        
        # 打印测试结果摘要
        self._print_test_summary(all_results)
        
        return all_results
    
    def _print_test_summary(self, results: Dict[str, Any]):
        """打印测试结果摘要"""
        logger.info("\n" + "=" * 60)
        logger.info("🧪 测试结果摘要")
        logger.info("=" * 60)
        
        total_tests = 0
        passed_tests = 0
        
        for category, category_results in results.items():
            logger.info(f"\n📋 {category}:")
            
            if isinstance(category_results, dict):
                for test_name, result in category_results.items():
                    total_tests += 1
                    if result:
                        passed_tests += 1
                        logger.info(f"   ✅ {test_name}")
                    else:
                        logger.info(f"   ❌ {test_name}")
            else:
                total_tests += 1
                if category_results:
                    passed_tests += 1
                    logger.info(f"   ✅ {category}")
                else:
                    logger.info(f"   ❌ {category}")
        
        logger.info(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            logger.info("🎉 所有测试通过!")
        else:
            logger.warning(f"⚠️ {total_tests - passed_tests} 个测试失败")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='命令行功能测试')
    parser.add_argument('--log_level', type=str, default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 创建测试器并运行测试
    tester = CLITester()
    results = tester.run_all_tests()
    
    # 检查是否有失败的测试
    all_passed = all(
        all(r.values()) if isinstance(r, dict) else r
        for r in results.values()
    )
    
    if not all_passed:
        sys.exit(1)


if __name__ == "__main__":
    main()
